

Objetivo.

Dado un texto propuesto por un usuario del tipo “quiero buscar zapatos negros con tacón alto para mujer”, encontrar los productos que responden lo más cercano posible a esta descripción.

Implementación.

Dado una empresa o país, cada que se cree una nueva imagen, se calcula con un LLM una descripción suficientemente detallada de esta, y se obtiene un EMBEDDING de ese texto.  Todo esto se almacena en una base de datos Postgress con pgvector().

<PERSON><PERSON>, cuando llegue una pregunta textual Q de un cliente, usando el mecanismo de embedding anterior se obtiene su embedding, y utilizando los índices vectoriales se encuentran los EMBEDDINGS de los textos de las imágenes más cercanas y se retornan los ID de Photos y Productos más cercanos.

Sobre la base de datos, se crea una tabla de IMAGENES por cada país/empresa (eh: IMAGENES_COLOMBIA) con la misma estructura, y se crea una vista IMAGENES que está basada en un UNION ALL de todas las tablas.

Cuando se hace un query  sobre IMAGENES dada una empresa y un embedding (estos van en el where), Postgres sabe qué tabla usar y utilizará el índice adecuado.

Esto también hace que sea fácil re-crear un indice por cada país sin forzar recrearlos todos al tiempo, manteniendo el codigo limpio.

Un nuevo país/empresa es solo una nueva tabla. y adicionarla a la vista IMAGENES

Las inserciones de nuevas imagenes si deben hacerse por separado en cada tabla país y no en la global.



Base de datos:  idea general

Lo primero es asegurar que la extensión pgVector se encuentra instalada:

CREATE EXTENSION IF NOT EXISTS vector;

Luego se crea la tabla por cada paìs:

CREATE TABLE IMAGENES_COLOMBIA (
    empresa VARCHAR(255) NOT NULL,          -- Nombre de la empresa (parte de la PK)
    idProducto INT NOT NULL,                -- Identificador del producto (parte de la PK)
    idPhoto INT NOT NULL,                   -- Identificador de la foto (parte de la PK)
    urlPhoto TEXT,                          -- URL de la imagen (puede ser NULL)
    texto TEXT,                             -- Texto asociado a la imagen (puede ser NULL)
    embedding VECTOR(1536),                 -- Vector de embedding (ojo tamaño)
    PRIMARY KEY (empresa, idProducto, idPhoto) -- Clave primaria compuesta
);





Recordar que empresa realmente se refiere a un pais (Dropi Colombia, Dropi México) o una empresa (Estrellas)



y se crean el indice vectorial (el de PK ya fue creado con al definición del constraint)

CREATE INDEX idx_imagenes_colombia ON IMAGENES_COLOMBIA USING HNSW (embedding vector_cosine_ops);

Se usa distancia coseno en vez de euclidiana pues la literatura indica que la magnitud no es importante.   O sea, el vector [1,2] y el vector [2,4] son considerados iguales.

y se crea la vista global:

CREATE VIEW IMAGENES AS
SELECT empresa, idProducto, idPhoto, urlPhoto, embedding
FROM IMAGENES_COLOMBIA
UNION ALL
SELECT empresa, idProducto, idPhoto, urlPhoto, embedding
FROM IMAGENES_PERU;



Las consultas dada una empresa, un embeddgin y un N así:



SELECT
    empresa,
    idProducto,
    idPhoto,
    urlPhoto,
    texto, 
    embedding < – > p_embedding AS distancia_coseno 
FROM
    IMAGENES 
WHERE
    empresa = p_empresa
ORDER BY
    distancia_coseno
LIMIT N;

End Points:

Se crean 4 end-points.  

el primero permite crear un nuevo país/empresa.   Este borra la tabla de la empresa si esta existe y la crea de nuevo y (borra) y crea la vista para incluirla.  crea también el indice vectorial asociado en la tabla de la empresa/pais

re-crea el indice de un país

inserta una imagen en un país.  Se calcula el embedding con AWS TITAN y se crea una nueva fila en la tabla correspondiente.  si la fila ya existe se borra primero

dado una empresa y un embedding, se traen las n más cercanas producto-idProducto

 



