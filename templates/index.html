<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo de Búsqueda Semántica</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/static/css/style.css">
</head>
<body>
    <div class="container mt-5">
        <h1 class="text-center mb-4">Demo de Búsqueda Semántica POC</h1>
        
        <!-- Sección para subir imágenes -->
        <div class="card mb-4">
            <div class="card-header">
                <h2 class="h5 mb-0">1. Subir Producto</h2>
            </div>
            <div class="card-body">
                <form id="uploadForm" class="row g-3">
                    <div class="col-md-6">
                        <label for="imageFile" class="form-label">Selecciona una imagen del producto</label>
                        <input type="file" class="form-control" id="imageFile" accept="image/*" required>
                    </div>
                    <div class="col-md-4">
                        <label for="empresa" class="form-label">Empresa</label>
                        <select class="form-control" id="empresa" required>
                            <option value="">Selecciona una empresa</option>
                            <option value="Empresa A">Empresa A</option>
                            <option value="Empresa B">Empresa B</option>
                            <option value="Empresa C">Empresa C</option>
                            <option value="Otra">Otra</option>
                        </select>
                    </div>
                    <div class="col-md-2 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary w-100">Subir</button>
                    </div>
                </form>

                <!-- Sección para mostrar la descripción generada -->
                <div id="descriptionPreview" class="mt-4" style="display: none;">
                    <div class="alert alert-info">
                        <h6 class="alert-heading">Descripción generada automáticamente:</h6>
                        <p id="generatedDescription" class="mb-2"></p>
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-success btn-sm" id="confirmDescription">Confirmar y Guardar</button>
                            <button type="button" class="btn btn-secondary btn-sm" id="cancelUpload">Cancelar</button>
                        </div>
                    </div>
                </div>

                <!-- Sección para mostrar errores -->
                <div id="errorMessage" class="mt-3" style="display: none;">
                    <div class="alert alert-danger">
                        <h6 class="alert-heading">Error:</h6>
                        <p id="errorText" class="mb-0"></p>
                    </div>
                </div>

                <!-- Información sobre formatos soportados -->
                <div class="mt-3">
                    <small class="text-muted">
                        <strong>Formatos soportados:</strong> JPG, PNG, GIF, WebP (máximo 10MB)
                    </small>
                </div>
            </div>
        </div>
        
        <!-- Sección para mostrar productos -->
        <div class="card mb-4">
            <div class="card-header">
                <h2 class="h5 mb-0">2. Productos con descripciones automáticas</h2>
            </div>
            <div class="card-body">
                <div id="imageGallery" class="row">
                    <!-- Los productos se cargarán aquí dinámicamente -->
                    <div class="col-12 text-center" id="noImagesMessage">
                        <p class="text-muted">No hay productos cargados aún.</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Sección para hacer preguntas -->
        <div class="card mb-4">
            <div class="card-header">
                <h2 class="h5 mb-0">3. Haz una pregunta</h2>
            </div>
            <div class="card-body">
                <form id="searchForm" class="row g-3">
                    <div class="col-md-10">
                        <input type="text" class="form-control" id="searchQuery"
                               placeholder="Ej: Busco un vestido de baño para mujer de color azul" required>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary w-100">Buscar</button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Sección para mostrar resultados -->
        <div class="card mb-4" id="resultsSection" style="display: none;">
            <div class="card-header">
                <h2 class="h5 mb-0">4. Resultados de la búsqueda</h2>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h3 class="h6">Consulta original:</h3>
                    <p id="originalQuery" class="mb-1"></p>
                    <h3 class="h6">Consulta procesada:</h3>
                    <p id="cleanQuery" class="mb-3"></p>
                </div>
                
                <h3 class="h6 mb-3">Resultados ordenados por relevancia (menor distancia = más relevante):</h3>
                <div id="searchResults" class="row">
                    <!-- Los resultados se cargarán aquí dinámicamente -->
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/main.js"></script>
</body>
</html>
