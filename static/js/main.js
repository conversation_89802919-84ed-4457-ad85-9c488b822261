document.addEventListener('DOMContentLoaded', function() {
    // Cargar imágenes existentes al iniciar
    loadImages();

    // Configurar el formulario de carga de imágenes
    const uploadForm = document.getElementById('uploadForm');
    uploadForm.addEventListener('submit', function(e) {
        e.preventDefault();
        uploadImage();
    });

    // Configurar el formulario de búsqueda
    const searchForm = document.getElementById('searchForm');
    searchForm.addEventListener('submit', function(e) {
        e.preventDefault();
        searchImages();
    });

    // Configurar botones de confirmación de descripción
    document.getElementById('confirmDescription').addEventListener('click', confirmAndSaveProduct);
    document.getElementById('cancelUpload').addEventListener('click', cancelUpload);
});

// Variable global para almacenar datos temporales del producto
let tempProductData = null;

// Función para cargar imágenes existentes
async function loadImages() {
    try {
        const response = await fetch('/images');
        const images = await response.json();

        updateImageGallery(images);
    } catch (error) {
        console.error('Error al cargar imágenes:', error);
    }
}

// Función para subir una imagen y generar descripción
async function uploadImage() {
    const fileInput = document.getElementById('imageFile');
    const empresaInput = document.getElementById('empresa');

    if (!fileInput.files[0] || !empresaInput.value) {
        alert('Por favor selecciona una imagen y especifica la empresa');
        return;
    }

    // Validar formato de archivo
    const file = fileInput.files[0];
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
        alert('Formato de imagen no válido. Por favor usa JPG, PNG, GIF o WebP');
        return;
    }

    // Validar tamaño de archivo (máximo 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
        alert('La imagen es demasiado grande. El tamaño máximo es 10MB');
        return;
    }

    const formData = new FormData();
    formData.append('file', fileInput.files[0]);
    formData.append('empresa', empresaInput.value);

    try {
        // Mostrar indicador de carga
        const submitBtn = uploadForm.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;
        submitBtn.textContent = 'Analizando imagen...';
        submitBtn.disabled = true;

        const response = await fetch('/generate-description', {
            method: 'POST',
            body: formData
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || `Error del servidor: ${response.status}`);
        }

        const result = await response.json();

        if (result.error) {
            throw new Error(result.error);
        }

        // Almacenar datos temporales
        tempProductData = {
            file: fileInput.files[0],
            empresa: empresaInput.value,
            descripcion: result.descripcion,
            imagen_base64: result.imagen_base64
        };

        // Mostrar la descripción generada para confirmación
        document.getElementById('generatedDescription').textContent = result.descripcion;
        document.getElementById('descriptionPreview').style.display = 'block';

        // Restaurar el botón
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;

    } catch (error) {
        console.error('Error al procesar la imagen:', error);
        showError('Error al procesar la imagen: ' + error.message);

        // Restaurar el botón en caso de error
        const submitBtn = uploadForm.querySelector('button[type="submit"]');
        submitBtn.textContent = 'Subir';
        submitBtn.disabled = false;
    }
}

// Función para mostrar errores de manera amigable
function showError(message) {
    const errorDiv = document.getElementById('errorMessage');
    const errorText = document.getElementById('errorText');

    errorText.textContent = message;
    errorDiv.style.display = 'block';

    // Ocultar el error después de 5 segundos
    setTimeout(() => {
        errorDiv.style.display = 'none';
    }, 5000);

    // Desplazarse al error
    errorDiv.scrollIntoView({ behavior: 'smooth' });
}
}

// Función para confirmar y guardar el producto
async function confirmAndSaveProduct() {
    if (!tempProductData) {
        alert('No hay datos de producto para guardar');
        return;
    }

    try {
        const formData = new FormData();
        formData.append('file', tempProductData.file);
        formData.append('empresa', tempProductData.empresa);
        formData.append('descripcion', tempProductData.descripcion);

        const response = await fetch('/upload-image', {
            method: 'POST',
            body: formData
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || `Error del servidor: ${response.status}`);
        }

        const result = await response.json();

        if (result.error) {
            throw new Error(result.error);
        }

        // Actualizar la galería con el nuevo producto
        loadImages();

        // Limpiar el formulario y ocultar la vista previa
        document.getElementById('imageFile').value = '';
        document.getElementById('empresa').value = '';
        document.getElementById('descriptionPreview').style.display = 'none';
        tempProductData = null;

        alert('Producto guardado exitosamente');

    } catch (error) {
        console.error('Error al guardar el producto:', error);
        showError('Error al guardar el producto: ' + error.message);
    }
}

// Función para cancelar la subida
function cancelUpload() {
    // Limpiar datos temporales y ocultar vista previa
    tempProductData = null;
    document.getElementById('descriptionPreview').style.display = 'none';
    document.getElementById('errorMessage').style.display = 'none';
    document.getElementById('imageFile').value = '';
    document.getElementById('empresa').value = '';
}

// Función para actualizar la galería de imágenes
function updateImageGallery(images) {
    const gallery = document.getElementById('imageGallery');

    // Limpiar la galería actual
    gallery.innerHTML = '';

    if (images.length === 0) {
        // Crear mensaje de "no hay imágenes"
        const noImagesMessage = document.createElement('div');
        noImagesMessage.className = 'col-12 text-center';
        noImagesMessage.id = 'noImagesMessage';
        noImagesMessage.innerHTML = '<p class="text-muted">No hay imágenes cargadas aún.</p>';
        gallery.appendChild(noImagesMessage);
        return;
    }

    // Agregar cada producto a la galería
    images.forEach(img => {
        const col = document.createElement('div');
        col.className = 'col-md-4 mb-3';

        col.innerHTML = `
            <div class="card h-100">
                <img src="data:image/jpeg;base64,${img.imagen_base64}" class="card-img-top" alt="${img.nombre}">
                <div class="card-body">
                    <h5 class="card-title">${img.nombre}</h5>
                    <p class="card-text"><strong>Descripción:</strong> ${img.descripcion}</p>
                    <p class="card-text"><strong>Empresa:</strong> ${img.empresa}</p>
                </div>
            </div>
        `;

        gallery.appendChild(col);
    });
}

// Función para buscar imágenes
async function searchImages() {
    const query = document.getElementById('searchQuery').value;

    if (!query) {
        alert('Por favor ingresa una consulta de búsqueda');
        return;
    }

    try {
        // Mostrar indicador de carga
        const submitBtn = searchForm.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;
        submitBtn.textContent = 'Buscando...';
        submitBtn.disabled = true;

        const response = await fetch('/search', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ query })
        });

        const result = await response.json();

        // Mostrar los resultados
        displaySearchResults(result);

        // Restaurar el botón
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;

    } catch (error) {
        console.error('Error al realizar la búsqueda:', error);
        alert('Error al realizar la búsqueda. Por favor intenta de nuevo.');

        // Restaurar el botón en caso de error
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
    }
}

// Función para mostrar los resultados de la búsqueda
function displaySearchResults(data) {
    // Mostrar la sección de resultados
    const resultsSection = document.getElementById('resultsSection');
    resultsSection.style.display = 'block';

    // Actualizar información de la consulta
    document.getElementById('originalQuery').textContent = data.query_original;
    document.getElementById('cleanQuery').textContent = data.query_limpia;

    // Mostrar los resultados
    const resultsContainer = document.getElementById('searchResults');
    resultsContainer.innerHTML = '';

    if (data.resultados.length === 0) {
        resultsContainer.innerHTML = '<div class="col-12"><p class="text-muted">No se encontraron resultados.</p></div>';
        return;
    }

    // Agregar cada resultado
    data.resultados.forEach(result => {
        const col = document.createElement('div');
        col.className = 'col-md-4 mb-3';

        col.innerHTML = `
            <div class="card h-100">
                <img src="data:image/jpeg;base64,${result.imagen_base64}" class="card-img-top" alt="${result.nombre}">
                <div class="card-body">
                    <h5 class="card-title">${result.nombre}</h5>
                    <p class="card-text"><strong>Descripción:</strong> ${result.descripcion}</p>
                    <p class="card-text"><strong>Empresa:</strong> ${result.empresa}</p>
                    <p class="card-text"><strong>Relevancia:</strong> ${result.distancia.toFixed(6)}</p>
                </div>
            </div>
        `;

        resultsContainer.appendChild(col);
    });

    // Desplazarse a la sección de resultados
    resultsSection.scrollIntoView({ behavior: 'smooth' });
}
