{"cells": [{"cell_type": "markdown", "id": "4240dcc4", "metadata": {}, "source": ["### **Instalar**\n", "* pip install ipywidgets\n", "* pip install python-dotenv\n", "* pip install boto3\n", "* pip install Pillow\n", "* pip install numpy"]}, {"cell_type": "code", "execution_count": 57, "id": "1573ca56", "metadata": {}, "outputs": [], "source": ["# para usar AWS Bedrock para procesamiento de texto e imágenes\n", "import os\n", "import json\n", "from dotenv import load_dotenv\n", "import boto3\n", "import numpy as np\n", "\n", "# para Imagenes\n", "from PIL import Image\n", "import base64\n", "\n"]}, {"cell_type": "code", "execution_count": 58, "id": "4cf5e438", "metadata": {}, "outputs": [], "source": ["# Cargar las variables de entorno desde el archivo .env\n", "load_dotenv()\n", "gemini_api_key = os.getenv(\"GEMINI_API_KEY\")\n", "genai.configure(api_key=gemini_api_key)"]}, {"cell_type": "code", "execution_count": 59, "id": "e703f35b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["bikini.jpg: Un bikini blanco de dos piezas con un top anudado y una parte inferior de talle alto.\n", "\n", "lucesNavidad.jpg: Luces LED RGB de tira de 65.6 pies con control remoto.\n", "\n", "reloj.jpg: Re<PERSON><PERSON> de pulsera para hombre con correa de acero inoxidable, diseño clásico, esfera negra con diamantes de imitación y calendario.\n", "\n"]}], "source": ["# Vamos a hacer que el LLM describa una imagen\n", "modelImagen = genai.GenerativeModel('gemini-1.5-flash')\n", "def describeImagen(ruta_imagen):\n", "    img = Image.open(ruta_imagen)\n", "\n", "    contents = [\n", "        img,\n", "        \"\"\"retorna en una frase la descripción del producto de la foto, en español.  \n", "        No incluyas en la respuesta nada más excepto la descripcion.\"\"\"\n", "    ]\n", "    response = modelImagen.generate_content(contents)\n", "\n", "    return response.text\n", "\n", "imagen='bikini.jpg'\n", "texto1 = describeImagen(imagen)\n", "print(imagen+':', texto1)\n", "\n", "imagen='lucesNavidad.jpg'\n", "texto2 = describeImagen(imagen)\n", "print(imagen+':', texto2)\n", "\n", "imagen='reloj.jpg'\n", "texto3 = describeImagen(imagen)\n", "print(imagen+':', texto3)\n"]}, {"cell_type": "code", "execution_count": null, "id": "179251b0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["texto original vestido de baño para mujer que cueste mayor de 100.000 pesos\n", "texto final:  <PERSON>est<PERSON> de baño para mujer.\n", "\n", "precios: <PERSON><PERSON> precio: 100.000\n", "Mayor precio: No especificado\n", "\n"]}], "source": ["# Ahora vamos a recibir la pregunta que va a hacer el cliente\n", "modelTextos = genai.GenerativeModel('gemini-1.5-pro-latest')\n", "\n", "def quitarTextosInnecesarios(prompt):  \n", "    \"\"\"Esta funcion toma un texto que viene prompt y hace dos cosas:  \n", "    1.  obtiene información de precios minimos y máximos\n", "    2.  quita esa referencia del prompt para que quede solo lo del producto\"\"\"\n", "\n", "    precios = f\"dado el siguiente texto, retorna UNICAMENTE el menor precio y el mayor precio buscado: '{prompt}'\"\n", "    precios = modelTextos.generate_content(precios)\n", "    prompt = f\"dado el siguiente texto, quita toda referencia a precios y devuelve el texto limpio: '{prompt}'\"\n", "    response = modelTextos.generate_content(prompt)\n", "    return response.text, precios.text\n", "\n", "# Textos a utilizar \n", "textoBase = \"vestido de baño para mujer que cueste mayor de 100.000 pesos y mayor a 200.000 pesos\"\n", "\n", "texto, precios  = quitarTextosInnecesarios(textoBase)\n", "\n", "print('texto original', textoBase)\n", "print('texto final: ', texto)\n", "print('precios:', precios)"]}, {"cell_type": "code", "execution_count": 62, "id": "e8803093", "metadata": {}, "outputs": [], "source": ["# ahora vamos a obtener embeddings tanto del texto final, como de los texto1 hasta texto3\n", "# Elegir un modelo pre-entrenado (multilingual para español)\n", "# modelo_nombre = \"bert-base-multilingual-cased\"\n", "modelo_nombre=\"sentence-transformers/paraphrase-multilingual-mpnet-base-v2\"\n", "tokenizer = AutoTokenizer.from_pretrained(modelo_nombre)\n", "modelo = AutoModel.from_pretrained(modelo_nombre)\n", "\n", "# y definimos una función que dado un texto devuelve su embedding\n", "def obt<PERSON><PERSON><PERSON><PERSON>(texto):\n", "\n", "    inputs = tokenizer(texto, return_tensors=\"pt\")\n", "    outputs = modelo(**inputs)\n", "\n", "    # El embedding del último hidden state (puedes necesitar procesamiento adicional)\n", "    embeddings = outputs.last_hidden_state\n", "    # print(\"Embeddings:\", embeddings.shape) # (batch_size, sequence_length, hidden_size)\n", "\n", "    # Para obtener un embedding de la frase, a menudo se toma el embedding del token [CLS]\n", "    embedding_frase = embeddings[0, 0, :].detach().numpy()\n", "    return embedding_frase"]}, {"cell_type": "code", "execution_count": 65, "id": "0852ccd5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Vestido de baño para mujer.\n", "\n", "Un bikini blanco de dos piezas con un top anudado y una parte inferior de talle alto.\n", "\n", "Luces LED RGB de tira de 65.6 pies con control remoto.\n", "\n", "<PERSON><PERSON>j de pulsera para hombre con correa de acero inoxidable, diseño clásico, esfera negra con diamantes de imitación y calendario.\n", "\n"]}], "source": ["print(texto)\n", "print(texto1)\n", "print(texto2)\n", "print(texto3)"]}, {"cell_type": "code", "execution_count": 66, "id": "d8712a52", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["buscando:   Vestido de baño para mujer.\n", " y precios encontrados: <PERSON><PERSON> precio: 100.000\n", "Mayor precio: 200.000\n", "\n", "d1: 2.00650167 - Un bikini blanco de dos piezas con un top anudado y una parte inferior de talle alto.\n", "\n", "d2: 2.70445395 - Luces LED RGB de tira de 65.6 pies con control remoto.\n", "\n", "d3: 2.77817082 - <PERSON><PERSON><PERSON> <PERSON> para hombre con correa de acero inoxidable, diseño clásico, esfera negra con diamantes de imitación y calendario.\n", "\n"]}], "source": ["e = obtenerEmbedding(texto)\n", "e1 = obtenerEmbedding(texto1)\n", "e2 = obtenerEmbedding(texto2)\n", "e3 = obtenerEmbedding(texto3)\n", "\n", "\n", "# y calculamos las distancias\n", "d1 = np.linalg.norm(e - e1)\n", "d2 = np.linalg.norm(e - e2)\n", "d3 = np.linalg.norm(e - e3)\n", "\n", "\n", "print(f\"buscando:   {texto} y precios encontrados: {precios}\")\n", "print(f\"d1: {d1:.8f} - {texto1}\")\n", "print(f\"d2: {d2:.8f} - {texto2}\")\n", "print(f\"d3: {d3:.8f} - {texto3}\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "962fe8e7", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 5}