# Migration Guide: Google Gemini to AWS Bedrock

This guide documents the migration from Google Gemini to AWS Bedrock for the semantic search application.

## Overview

The application has been migrated from:
- **Google Gemini API** → **AWS Bedrock**
- **Gemini 1.5 Flash** → **Amazon Nova Lite**
- **Sentence Transformers** → **Amazon Titan Text Embeddings**

## Key Changes

### 1. Dependencies
- **Removed**: `google-generativeai`, `transformers`, `torch`, `sentence-transformers`
- **Added**: `boto3` for AWS SDK

### 2. Authentication
- **Before**: `GEMINI_API_KEY` environment variable
- **After**: AWS credentials (IAM roles, access keys, or AWS CLI)

### 3. Models
- **Image Description**: Gemini 1.5 Flash → Amazon Nova Lite (`amazon.nova-lite-v1:0`)
- **Text Processing**: Gemini 1.5 Flash → Amazon Nova Lite (`amazon.nova-lite-v1:0`)
- **Embeddings**: Sentence Transformers → Amazon Titan Text Embeddings (`amazon.titan-embed-text-v1`)

### 4. API Changes
- **Before**: `genai.GenerativeModel().generate_content()`
- **After**: `bedrock_client.invoke_model()` with JSON payloads

## Prerequisites for AWS Bedrock

### 1. AWS Account Setup
1. Ensure you have an AWS account with appropriate permissions
2. Configure AWS credentials using one of these methods:
   - AWS CLI: `aws configure`
   - Environment variables: `AWS_ACCESS_KEY_ID`, `AWS_SECRET_ACCESS_KEY`
   - IAM roles (recommended for production)

### 2. Bedrock Model Access
1. Go to AWS Bedrock console
2. Request access to the following models:
   - **Amazon Nova Lite** (`amazon.nova-lite-v1:0`)
   - **Amazon Titan Text Embeddings** (`amazon.titan-embed-text-v1`)
3. Wait for approval (usually instant for these models)

### 3. IAM Permissions
Ensure your AWS credentials have the following permissions:
```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "bedrock:InvokeModel",
                "bedrock:ListFoundationModels"
            ],
            "Resource": "*"
        }
    ]
}
```

## Configuration Changes

### Environment Variables
Update your `.env` file:
```bash
# Old configuration
GEMINI_API_KEY=your_gemini_api_key_here

# New configuration
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_aws_access_key_here
AWS_SECRET_ACCESS_KEY=your_aws_secret_key_here
```

### Infrastructure
- **CDK**: Updated to include Bedrock IAM permissions
- **Terraform**: Replaced Gemini API key with Bedrock permissions
- **Docker**: Removed model pre-downloading, added AWS SDK

## Testing the Migration

### 1. Health Check
```bash
curl http://localhost:8000/health
```
Should return:
```json
{
    "status": "ok",
    "bedrock_connected": true,
    "version": "1.0.0",
    "timestamp": "2024-01-01T00:00:00"
}
```

### 2. Image Upload
Test image description functionality by uploading an image through the web interface.

### 3. Search
Test semantic search by querying for products.

## Troubleshooting

### Common Issues

1. **Bedrock Access Denied**
   - Verify IAM permissions
   - Check if models are approved in Bedrock console
   - Ensure correct AWS region

2. **Model Not Found**
   - Verify model IDs are correct
   - Check model availability in your region
   - Ensure model access has been granted

3. **Authentication Errors**
   - Verify AWS credentials are configured
   - Check credential permissions
   - Ensure credentials are not expired

### Debugging
Enable debug logging by setting:
```bash
export AWS_DEFAULT_REGION=us-east-1
export PYTHONPATH=/app
```

## Performance Considerations

### Response Times
- **Nova Lite**: Similar to Gemini 1.5 Flash
- **Titan Embeddings**: Faster than Sentence Transformers (no local processing)

### Costs
- **Nova Lite**: Pay-per-use, typically lower cost than Gemini
- **Titan Embeddings**: Very cost-effective for embedding generation

### Scaling
- AWS Bedrock automatically scales
- No need to manage model infrastructure
- Built-in rate limiting and throttling

## Rollback Plan

If you need to rollback to Gemini:
1. Restore the original `app.py` from git history
2. Update `requirements.txt` to include Gemini dependencies
3. Restore environment variables
4. Redeploy the application

## Next Steps

1. Monitor application performance
2. Optimize prompts for Nova Lite if needed
3. Consider using other Bedrock models for enhanced capabilities
4. Implement proper error handling and retry logic
5. Set up CloudWatch monitoring for Bedrock usage
