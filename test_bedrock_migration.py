#!/usr/bin/env python3
"""
Test script to validate AWS Bedrock migration
"""

import os
import json
import boto3
import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_aws_credentials():
    """Test AWS credentials and Bedrock access"""
    print("🔍 Testing AWS credentials...")
    
    try:
        # Test basic AWS access
        sts = boto3.client('sts')
        identity = sts.get_caller_identity()
        print(f"✅ AWS credentials valid. Account: {identity['Account']}")
        
        # Test Bedrock access (Bedrock is available in us-east-1)
        bedrock = boto3.client('bedrock', region_name='us-east-2')
        models = bedrock.list_foundation_models()
        print(f"✅ Bedrock access confirmed. Found {len(models['modelSummaries'])} models")
        
        # Check specific models
        nova_lite_found = False
        titan_embed_found = False

        for model in models['modelSummaries']:
            if model['modelId'] == 'amazon.nova-lite-v1:0':
                nova_lite_found = True
            elif model['modelId'] == 'amazon.titan-embed-text-v2:0':
                titan_embed_found = True

        # Check inference profiles for Nova Lite (required in us-east-2)
        try:
            profiles = bedrock.list_inference_profiles()
            for profile in profiles['inferenceProfileSummaries']:
                if profile['inferenceProfileId'] == 'us.amazon.nova-lite-v1:0':
                    nova_lite_found = True
                    break
        except Exception as e:
            print(f"Could not check inference profiles: {e}")
        
        if nova_lite_found:
            print("✅ Amazon Nova Lite model available")
        else:
            print("❌ Amazon Nova Lite model not found")
            
        if titan_embed_found:
            print("✅ Amazon Titan Text Embeddings model available")
        else:
            print("❌ Amazon Titan Text Embeddings model not found")
            
        return nova_lite_found and titan_embed_found
        
    except Exception as e:
        print(f"❌ AWS/Bedrock access failed: {e}")
        return False

def test_nova_lite():
    """Test Nova Lite model directly"""
    print("\n🧪 Testing Nova Lite model...")
    
    try:
        bedrock_runtime = boto3.client('bedrock-runtime', region_name='us-east-2')

        # Correct format for Nova Lite
        request_body = {
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "text": "Describe a red apple in Spanish in one sentence."
                        }
                    ]
                }
            ],
            "inferenceConfig": {
                "maxTokens": 100,
                "temperature": 0.1
            }
        }

        response = bedrock_runtime.invoke_model(
            modelId="us.amazon.nova-lite-v1:0",  # Inference profile in us-east-2
            body=json.dumps(request_body)
        )

        response_body = json.loads(response['body'].read())
        result = response_body['output']['message']['content'][0]['text'].strip()
        print(f"✅ Nova Lite response: {result}")
        return True
        
    except Exception as e:
        error_msg = str(e)
        if "AccessDeniedException" in error_msg:
            print(f"❌ Nova Lite test failed: Access denied. You need to request access to this model in the AWS Bedrock console.")
            print("   Go to: https://console.aws.amazon.com/bedrock/home?region=us-east-2#/modelaccess")
        else:
            print(f"❌ Nova Lite test failed: {e}")
        return False

def test_titan_embeddings():
    """Test Titan Text Embeddings model directly"""
    print("\n🧪 Testing Titan Text Embeddings...")
    
    try:
        bedrock_runtime = boto3.client('bedrock-runtime', region_name='us-east-2')
        
        request_body = {
            "inputText": "This is a test sentence for embedding generation."
        }
        
        response = bedrock_runtime.invoke_model(
            modelId="amazon.titan-embed-text-v2:0",
            body=json.dumps(request_body)
        )
        
        response_body = json.loads(response['body'].read())
        embedding = response_body['embedding']
        print(f"✅ Titan Embeddings response: {len(embedding)} dimensions")
        return True
        
    except Exception as e:
        error_msg = str(e)
        if "AccessDeniedException" in error_msg:
            print(f"❌ Titan Embeddings test failed: Access denied. You need to request access to this model in the AWS Bedrock console.")
            print("   Go to: https://console.aws.amazon.com/bedrock/home?region=us-east-2#/modelaccess")
        else:
            print(f"❌ Titan Embeddings test failed: {e}")
        return False

def test_application_health(base_url="http://localhost:8000"):
    """Test application health endpoint"""
    print(f"\n🏥 Testing application health at {base_url}...")
    
    try:
        response = requests.get(f"{base_url}/health", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Health check passed: {data}")
            
            if data.get('bedrock_connected'):
                print("✅ Application reports Bedrock connection successful")
                return True
            else:
                print("❌ Application reports Bedrock connection failed")
                return False
        else:
            print(f"❌ Health check failed with status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Application health test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting AWS Bedrock Migration Tests")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 4
    
    # Test 1: AWS credentials and Bedrock access
    if test_aws_credentials():
        tests_passed += 1
    
    # Test 2: Nova Lite model
    if test_nova_lite():
        tests_passed += 1
    
    # Test 3: Titan Embeddings model
    if test_titan_embeddings():
        tests_passed += 1
    
    # Test 4: Application health (optional - only if app is running)
    print("\n🤔 Is the application running locally? (y/N)")
    response = input().strip().lower()
    if response in ['y', 'yes']:
        if test_application_health():
            tests_passed += 1
    else:
        print("⏭️  Skipping application health test")
        total_tests -= 1
    
    # Summary
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! Migration is successful.")
        return 0
    else:
        print("❌ Some tests failed. Please check the configuration.")
        return 1

if __name__ == "__main__":
    exit(main())
