# Demo de Búsqueda Semántica POC

Esta es una prueba de concepto (POC) para demostrar la búsqueda semántica utilizando imágenes y procesamiento de lenguaje natural con AWS Bedrock.

## Características

1. El usuario puede subir imágenes con su precio
2. El sistema calcula automáticamente la descripción de cada imagen utilizando Amazon Nova Lite
3. El usuario puede hacer preguntas en lenguaje natural
4. El sistema muestra las imágenes ordenadas por relevancia según la distancia semántica usando Amazon Titan Text Embeddings

## Requisitos

- Python 3.8+
- Credenciales de AWS configuradas
- Acceso a AWS Bedrock con los modelos Nova Lite y Titan Text Embeddings

## Instalación

1. Instalar las dependencias:

```bash
pip install -r requirements.txt
```

2. Configurar las credenciales de AWS. Puedes hacerlo de varias formas:

### Opción 1: Variables de entorno
Crea un archivo `.env` con tus credenciales de AWS:

```
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=tu_access_key_aqui
AWS_SECRET_ACCESS_KEY=tu_secret_key_aqui
```

### Opción 2: AWS CLI
```bash
aws configure
```

### Opción 3: IAM Roles (recomendado para producción)
Si ejecutas en EC2 o ECS, usa IAM roles en lugar de credenciales hardcodeadas.

## Configuración de AWS Bedrock

### Permisos requeridos
Tu usuario/rol de AWS necesita los siguientes permisos:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "bedrock:InvokeModel",
                "bedrock:ListFoundationModels"
            ],
            "Resource": "*"
        }
    ]
}
```

### Acceso a modelos
1. Ve a la consola de AWS Bedrock
2. Solicita acceso a los siguientes modelos:
   - **Amazon Nova Lite** (`amazon.nova-lite-v1:0`)
   - **Amazon Titan Text Embeddings** (`amazon.titan-embed-text-v1`)
3. Espera la aprobación (usualmente instantánea)

## Ejecución

```bash
python app.py
```

La aplicación estará disponible en `http://localhost:8000`

## Pruebas

Para validar que la migración a AWS Bedrock funciona correctamente:

```bash
python test_bedrock_migration.py
```

## Migración desde Gemini

Si estás migrando desde la versión anterior que usaba Google Gemini, consulta el archivo `MIGRATION_GUIDE.md` para instrucciones detalladas.

## Endpoints de la API

- **Health Check**: `GET /health` - Verifica el estado de la aplicación y conexión con Bedrock
- **Health Check Detallado**: `GET /health/detailed` - Información completa del sistema
- **Subir Imagen**: `POST /upload-image` - Sube una imagen con precio
- **Buscar**: `POST /search` - Busca imágenes por descripción
- **Listar Imágenes**: `GET /images` - Lista todas las imágenes almacenadas
- **Interfaz Web**: `GET /` - Interfaz web para interactuar con la aplicación

## Ejecución

Para iniciar la aplicación:

```bash
python app.py
```

La aplicación estará disponible en http://localhost:8000

## Uso

1. Abre la aplicación en tu navegador
2. Sube imágenes con sus precios correspondientes
3. Observa cómo el sistema genera automáticamente descripciones para cada imagen
4. Haz una pregunta en lenguaje natural (puedes incluir referencias a precios)
5. Observa los resultados ordenados por relevancia semántica
