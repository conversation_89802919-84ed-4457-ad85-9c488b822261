import pytest
import asyncio
from fastapi.testclient import Test<PERSON>lient
from app.main import app

client = TestClient(app)

def test_health_check():
    """Test health check endpoint"""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert "status" in data
    assert "timestamp" in data
    assert "database_connected" in data
    assert "openai_configured" in data

def test_root_endpoint():
    """Test root endpoint"""
    response = client.get("/")
    assert response.status_code == 200
    data = response.json()
    assert data["message"] == "Semantic Search POC API"
    assert data["version"] == "1.0.0"

def test_create_company():
    """Test company creation endpoint"""
    company_name = "TEST_COMPANY"
    response = client.post(f"/api/v1/companies/{company_name}/create", json={})
    
    # This might fail if database is not connected, but we test the endpoint structure
    assert response.status_code in [200, 500]  # 500 if DB not available
    
    if response.status_code == 200:
        data = response.json()
        assert data["company_name"] == company_name
        assert data["success"] is True

def test_create_company_invalid_name():
    """Test company creation with invalid name"""
    response = client.post("/api/v1/companies/ /create", json={})
    assert response.status_code == 400

def test_insert_image_nonexistent_company():
    """Test inserting image for non-existent company"""
    response = client.post(
        "/api/v1/companies/NONEXISTENT/images",
        json={
            "idProducto": 1,
            "idPhoto": 1,
            "urlPhoto": "https://example.com/image.jpg",
            "texto": "Test product description"
        }
    )
    assert response.status_code in [404, 500]  # 404 if company doesn't exist

def test_search_nonexistent_company():
    """Test search for non-existent company"""
    response = client.post(
        "/api/v1/search",
        json={
            "company_name": "NONEXISTENT",
            "query_text": "zapatos negros",
            "limit": 10
        }
    )
    assert response.status_code in [404, 500]  # 404 if company doesn't exist

def test_reindex_nonexistent_company():
    """Test reindexing non-existent company"""
    response = client.post("/api/v1/companies/NONEXISTENT/reindex", json={})
    assert response.status_code in [404, 500]  # 404 if company doesn't exist

# Integration test (requires database and OpenAI API)
@pytest.mark.integration
def test_full_workflow():
    """Test complete workflow: create company, insert image, search"""
    company_name = "COLOMBIA"
    
    # 1. Create company
    response = client.post(f"/api/v1/companies/{company_name}/create", json={})
    if response.status_code != 200:
        pytest.skip("Database not available for integration test")
    
    # 2. Insert image
    response = client.post(
        f"/api/v1/companies/{company_name}/images",
        json={
            "idProducto": 1,
            "idPhoto": 1,
            "urlPhoto": "https://example.com/zapatos.jpg",
            "texto": "Zapatos negros de tacón alto para mujer, elegantes y cómodos"
        }
    )
    if response.status_code != 200:
        pytest.skip("OpenAI API not available for integration test")
    
    # 3. Search
    response = client.post(
        "/api/v1/search",
        json={
            "company_name": company_name,
            "query_text": "busco zapatos negros con tacón para mujer",
            "limit": 5
        }
    )
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert len(data["results"]) > 0