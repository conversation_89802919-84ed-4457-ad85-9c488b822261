#!/bin/bash

# Deploy Semantic Search POC to AWS Fargate
# Cost-optimized deployment using Terraform and ECR

set -e

# Configuration
AWS_PROFILE="IA"
AWS_REGION="us-east-2"
PROJECT_NAME="semantic-search-poc"

echo "🚀 Starting Fargate deployment for Semantic Search POC"
echo "📍 Region: $AWS_REGION"
echo "👤 Profile: $AWS_PROFILE"
echo "📦 Project: $PROJECT_NAME"
echo ""

# Check AWS CLI configuration
echo "🔍 Checking AWS CLI configuration..."
if ! aws sts get-caller-identity --profile $AWS_PROFILE > /dev/null 2>&1; then
    echo "❌ Error: AWS CLI not configured for profile '$AWS_PROFILE'"
    echo "Please run: aws configure --profile $AWS_PROFILE"
    exit 1
fi

echo "✅ AWS CLI configured correctly"
echo ""

# Navigate to terraform directory
cd terraform

# Initialize Terraform
echo "🏗️  Initializing Terraform..."
terraform init

# Plan deployment
echo "📋 Planning Terraform deployment..."
terraform plan -var="aws_profile=$AWS_PROFILE" -var="aws_region=$AWS_REGION"

# Ask for confirmation
echo ""
read -p "🤔 Do you want to proceed with the deployment? (y/N): " -n 1 -r
echo ""
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Deployment cancelled"
    exit 1
fi

# Apply Terraform
echo "🚀 Applying Terraform configuration..."
terraform apply -auto-approve -var="aws_profile=$AWS_PROFILE" -var="aws_region=$AWS_REGION"

# Get ECR repository URL
ECR_REPO=$(terraform output -raw ecr_repository_url)
echo ""
echo "📦 ECR Repository: $ECR_REPO"

# Navigate back to project root
cd ..

# Login to ECR
echo "🔐 Logging into ECR..."
aws ecr get-login-password --region $AWS_REGION --profile $AWS_PROFILE | docker login --username AWS --password-stdin $ECR_REPO

# Build Docker image
echo "🔨 Building Docker image..."
docker build -t $PROJECT_NAME .

# Tag image for ECR
echo "🏷️  Tagging image for ECR..."
docker tag $PROJECT_NAME:latest $ECR_REPO:latest

# Push image to ECR
echo "📤 Pushing image to ECR..."
docker push $ECR_REPO:latest

# Update ECS service to use new image
echo "🔄 Updating ECS service..."
cd terraform
CLUSTER_NAME=$(terraform output -raw ecs_cluster_name)
SERVICE_NAME=$(terraform output -raw ecs_service_name)

# Force new deployment
aws ecs update-service \
    --cluster $CLUSTER_NAME \
    --service $SERVICE_NAME \
    --force-new-deployment \
    --region $AWS_REGION \
    --profile $AWS_PROFILE

echo ""
echo "✅ Deployment completed successfully!"
echo ""
echo "📊 Deployment Information:"
terraform output deployment_info
echo ""

# Wait for service to be stable
echo "⏳ Waiting for service to become stable..."
aws ecs wait services-stable \
    --cluster $CLUSTER_NAME \
    --services $SERVICE_NAME \
    --region $AWS_REGION \
    --profile $AWS_PROFILE

echo "✅ Service is now stable!"
echo ""

# Get public IP
echo "🌐 Getting public IP address..."
TASK_ARN=$(aws ecs list-tasks \
    --cluster $CLUSTER_NAME \
    --service-name $SERVICE_NAME \
    --region $AWS_REGION \
    --profile $AWS_PROFILE \
    --query 'taskArns[0]' \
    --output text)

if [ "$TASK_ARN" != "None" ] && [ "$TASK_ARN" != "" ]; then
    PUBLIC_IP=$(aws ecs describe-tasks \
        --cluster $CLUSTER_NAME \
        --tasks $TASK_ARN \
        --region $AWS_REGION \
        --profile $AWS_PROFILE \
        --query 'tasks[0].attachments[0].details[?name==`networkInterfaceId`].value' \
        --output text | xargs -I {} aws ec2 describe-network-interfaces \
        --network-interface-ids {} \
        --region $AWS_REGION \
        --profile $AWS_PROFILE \
        --query 'NetworkInterfaces[0].Association.PublicIp' \
        --output text)
    
    if [ "$PUBLIC_IP" != "None" ] && [ "$PUBLIC_IP" != "" ]; then
        echo "🎉 Application is accessible at: http://$PUBLIC_IP:8000"
        echo "🏥 Health check: http://$PUBLIC_IP:8000/health"
        echo "📚 API docs: http://$PUBLIC_IP:8000/docs"
    else
        echo "⚠️  Could not retrieve public IP. Check ECS console for task details."
    fi
else
    echo "⚠️  No running tasks found. Check ECS console for service status."
fi

echo ""
echo "💰 Cost Optimization Features:"
echo "   - Using Fargate Spot instances (up to 70% cost savings)"
echo "   - Minimal CPU/Memory allocation (256 CPU, 512 MB)"
echo "   - No Application Load Balancer (direct public IP access)"
echo "   - Minimal CloudWatch logging (1 day retention)"
echo "   - Using default VPC (no additional networking costs)"
echo ""
echo "🎯 Deployment complete! Your semantic search application is now running on AWS Fargate."
