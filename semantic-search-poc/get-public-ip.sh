#!/bin/bash

# Script para obtener la IP pública de la aplicación Fargate
set -e

AWS_REGION="us-east-2"
AWS_PROFILE="IA"
PROJECT_NAME="semantic-search-poc"

echo "🔍 Obteniendo IP pública de la aplicación..."

# Obtener ARN de la tarea en ejecución
TASK_ARN=$(aws ecs list-tasks \
    --cluster ${PROJECT_NAME}-cluster \
    --service-name ${PROJECT_NAME}-service \
    --desired-status RUNNING \
    --region ${AWS_REGION} \
    --profile ${AWS_PROFILE} \
    --query 'taskArns[0]' \
    --output text 2>/dev/null || echo "")

if [ "$TASK_ARN" = "None" ] || [ -z "$TASK_ARN" ]; then
    echo "❌ No hay tareas en ejecución"
    
    # Verificar tareas pendientes
    PENDING_TASKS=$(aws ecs list-tasks \
        --cluster ${PROJECT_NAME}-cluster \
        --service-name ${PROJECT_NAME}-service \
        --desired-status PENDING \
        --region ${AWS_REGION} \
        --profile ${AWS_PROFILE} \
        --query 'length(taskArns)' \
        --output text 2>/dev/null || echo "0")
    
    if [ "$PENDING_TASKS" -gt 0 ]; then
        echo "⏳ Hay $PENDING_TASKS tarea(s) pendiente(s). Esperando..."
        exit 1
    else
        echo "❌ No hay tareas ejecutándose ni pendientes"
        exit 1
    fi
fi

echo "📋 Tarea encontrada: $TASK_ARN"

# Obtener detalles de la tarea
NETWORK_INTERFACE_ID=$(aws ecs describe-tasks \
    --cluster ${PROJECT_NAME}-cluster \
    --tasks $TASK_ARN \
    --region ${AWS_REGION} \
    --profile ${AWS_PROFILE} \
    --query 'tasks[0].attachments[0].details[?name==`networkInterfaceId`].value' \
    --output text 2>/dev/null || echo "")

if [ -z "$NETWORK_INTERFACE_ID" ] || [ "$NETWORK_INTERFACE_ID" = "None" ]; then
    echo "❌ No se pudo obtener el ID de la interfaz de red"
    exit 1
fi

echo "🔌 Interfaz de red: $NETWORK_INTERFACE_ID"

# Obtener IP pública
PUBLIC_IP=$(aws ec2 describe-network-interfaces \
    --network-interface-ids $NETWORK_INTERFACE_ID \
    --region ${AWS_REGION} \
    --profile ${AWS_PROFILE} \
    --query 'NetworkInterfaces[0].Association.PublicIp' \
    --output text 2>/dev/null || echo "")

if [ -z "$PUBLIC_IP" ] || [ "$PUBLIC_IP" = "None" ]; then
    echo "❌ No se pudo obtener la IP pública"
    exit 1
fi

echo ""
echo "✅ ¡Aplicación desplegada exitosamente!"
echo "🌐 URL de acceso: http://$PUBLIC_IP:8000"
echo ""
echo "📊 Endpoints disponibles:"
echo "   • Página principal: http://$PUBLIC_IP:8000"
echo "   • Health check: http://$PUBLIC_IP:8000/health"
echo "   • API docs: http://$PUBLIC_IP:8000/docs"
echo ""
echo "💡 Nota: La aplicación puede tardar unos minutos en estar completamente lista."