#!/usr/bin/env python3
"""
Sample data script for testing the Semantic Search POC
"""

import requests
import json
import time

# Configuration
BASE_URL = "http://localhost:8001"
API_BASE = f"{BASE_URL}/api/v1"

# Sample data
COMPANIES = ["COLOMBIA", "PERU"]

SAMPLE_PRODUCTS = [
    {
        "idProducto": 1,
        "idPhoto": 1,
        "urlPhoto": "https://example.com/zapatos-negros-tacones.jpg",
        "texto": "Zapatos negros de tacón alto para mujer, elegantes y sofisticados, perfectos para ocasiones formales"
    },
    {
        "idProducto": 2,
        "idPhoto": 1,
        "urlPhoto": "https://example.com/vestido-azul.jpg",
        "texto": "Vestido azul marino de manga larga, estilo casual elegante, ideal para el día a día"
    },
    {
        "idProducto": 3,
        "idPhoto": 1,
        "urlPhoto": "https://example.com/reloj-dorado.jpg",
        "texto": "Reloj dorado unisex con correa de cuero marrón, diseño clásico y elegante"
    },
    {
        "idProducto": 4,
        "idPhoto": 1,
        "urlPhoto": "https://example.com/bikini-rojo.jpg",
        "texto": "Bikini rojo de dos piezas para mujer, ideal para la playa y piscina, talla M"
    },
    {
        "idProducto": 5,
        "idPhoto": 1,
        "urlPhoto": "https://example.com/luces-navidad.jpg",
        "texto": "Luces de Navidad LED multicolor, 100 bombillas, para decoración navideña interior y exterior"
    }
]

SAMPLE_QUERIES = [
    "zapatos negros con tacón alto para mujer",
    "vestido azul casual",
    "reloj elegante dorado",
    "bikini para la playa",
    "decoración navideña con luces"
]

def check_health():
    """Check if the API is running"""
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API is running - Status: {data['status']}")
            print(f"   Database connected: {data['database_connected']}")
            print(f"   AWS Bedrock configured: {data['openai_configured']}")  # Field name kept for compatibility
            return True
        else:
            print(f"❌ API health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Cannot connect to API: {e}")
        return False

def create_companies():
    """Create company tables"""
    print("\n🏢 Creating companies...")
    for company in COMPANIES:
        try:
            response = requests.post(f"{API_BASE}/companies/{company}/create", json={})
            if response.status_code == 200:
                print(f"✅ Created company: {company}")
            else:
                print(f"❌ Failed to create company {company}: {response.status_code}")
                print(f"   Response: {response.text}")
        except Exception as e:
            print(f"❌ Error creating company {company}: {e}")

def insert_sample_data():
    """Insert sample products for each company"""
    print("\n📦 Inserting sample products...")
    for company in COMPANIES:
        print(f"\n  Company: {company}")
        for product in SAMPLE_PRODUCTS:
            try:
                response = requests.post(
                    f"{API_BASE}/companies/{company}/images",
                    json=product
                )
                if response.status_code == 200:
                    data = response.json()
                    print(f"    ✅ Product {product['idProducto']}: {product['texto'][:50]}...")
                else:
                    print(f"    ❌ Failed to insert product {product['idProducto']}: {response.status_code}")
                    print(f"       Response: {response.text}")
            except Exception as e:
                print(f"    ❌ Error inserting product {product['idProducto']}: {e}")
            
            # Small delay to avoid overwhelming the API
            time.sleep(0.5)

def test_searches():
    """Test semantic searches"""
    print("\n🔍 Testing semantic searches...")
    for company in COMPANIES:
        print(f"\n  Company: {company}")
        for query in SAMPLE_QUERIES:
            try:
                response = requests.post(
                    f"{API_BASE}/search",
                    json={
                        "company_name": company,
                        "query_text": query,
                        "limit": 3
                    }
                )
                if response.status_code == 200:
                    data = response.json()
                    print(f"    🔍 Query: '{query}'")
                    print(f"       Found {data['total_results']} results:")
                    for i, result in enumerate(data['results'][:2], 1):
                        distance = result['distancia_coseno']
                        texto = result['texto'][:60] if result['texto'] else 'No description'
                        print(f"         {i}. Distance: {distance:.3f} - {texto}...")
                else:
                    print(f"    ❌ Search failed for '{query}': {response.status_code}")
            except Exception as e:
                print(f"    ❌ Error searching '{query}': {e}")
            
            time.sleep(0.5)

def main():
    """Main function to run all tests"""
    print("🚀 Semantic Search POC - Sample Data and Validation")
    print("=" * 60)
    
    # Check if API is running
    if not check_health():
        print("\n❌ API is not available. Make sure to:")
        print("   1. Start PostgreSQL with: docker-compose up -d")
        print("   2. Configure AWS CLI profile 'IA' with: aws configure --profile IA")
        print("   3. Set AWS_REGION=us-east-2 and AWS_PROFILE=IA in .env file")
        print("   4. Start the API with: python -m app.main")
        return
    
    # Run the workflow
    create_companies()
    insert_sample_data()
    test_searches()
    
    print("\n✅ Sample data validation completed!")
    print(f"\n📖 You can also test the API manually at: {BASE_URL}/docs")

if __name__ == "__main__":
    main()