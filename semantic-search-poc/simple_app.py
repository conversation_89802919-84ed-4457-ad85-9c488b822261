import os
import base64
import numpy as np
import platform
import time
import psutil
import json
from datetime import datetime
from fastapi import FastAPI, Request, File, UploadFile, Form, status
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
# from fastapi.templating import Jinja2Templates  # Not needed for simple HTML serving
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Dict, Optional, Any
import uvicorn
from dotenv import load_dotenv
import boto3
from botocore.exceptions import ClientError
from PIL import Image
import io

# Cargar las variables de entorno
load_dotenv()
aws_region = os.getenv("AWS_REGION", "us-east-2")  # Región para toda la infraestructura

# Configurar cliente de AWS Bedrock (usa la misma región)
bedrock_client = boto3.client(
    'bedrock-runtime',
    region_name=aws_region,
)

# Inicializar FastAPI
app = FastAPI(title="Demo de Búsqueda Semántica")

# Configurar CORS para permitir solicitudes desde el dashboard
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Permitir todos los orígenes (solo para POC)
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Configurar carpetas estáticas
app.mount("/static", StaticFiles(directory="static"), name="static")

# Configuración de modelos AWS Bedrock (disponibles en us-east-2)
NOVA_LITE_MODEL_ID = "us.amazon.nova-lite-v1:0"  # Inference profile for Nova Lite
TITAN_EMBEDDINGS_MODEL_ID = "amazon.titan-embed-text-v2:0"  # Latest Titan embeddings

# Variables para verificar la conexión
bedrock_connected = True  # Simplified for testing - will be verified during actual usage

# Almacenamiento en memoria para las imágenes y sus descripciones
imagenes_db = []

# Función para describir una imagen usando AWS Bedrock Nova Lite (modelo más barato)
def describeImagen(imagen_bytes):
    try:
        # Validar que los bytes de imagen no estén vacíos
        if not imagen_bytes:
            return "Error: imagen vacía"

        # Convertir imagen a base64
        encoded_image = base64.b64encode(imagen_bytes).decode('utf-8')

        # Preparar el prompt optimizado para Nova Lite
        prompt = """Describe este producto en español con detalles específicos sobre:
        - Tipo de producto
        - Colores principales
        - Material aparente
        - Estilo o características distintivas
        - Uso o función
        
        Responde solo con la descripción del producto, sin explicaciones adicionales."""

        # Preparar el cuerpo de la solicitud para Nova Lite
        request_body = {
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "image": {
                                "format": "jpeg",
                                "source": {
                                    "bytes": encoded_image
                                }
                            }
                        },
                        {
                            "text": prompt
                        }
                    ]
                }
            ],
            "inferenceConfig": {
                "maxTokens": 300,  # Aumentado para descripciones más detalladas
                "temperature": 0.2  # Ligeramente más creativo pero consistente
            }
        }

        # Llamar a Nova Lite (modelo más barato para visión)
        response = bedrock_client.invoke_model(
            modelId=NOVA_LITE_MODEL_ID,
            body=json.dumps(request_body)
        )

        # Procesar la respuesta
        response_body = json.loads(response['body'].read())
        descripcion = response_body['output']['message']['content'][0]['text'].strip()
        
        # Validar que la descripción no esté vacía
        if not descripcion or len(descripcion) < 10:
            return "Producto visible en la imagen"
            
        return descripcion

    except ClientError as e:
        error_code = e.response['Error']['Code']
        print(f"Error de AWS Bedrock: {error_code} - {e}")
        if error_code == 'ValidationException':
            return "Error: formato de imagen no válido"
        elif error_code == 'ThrottlingException':
            return "Error: servicio temporalmente no disponible"
        else:
            return "Error al analizar la imagen"
    except Exception as e:
        print(f"Error describiendo imagen con Nova Lite: {e}")
        return "Error al describir la imagen"

# Función para obtener embeddings usando Amazon Titan Text Embeddings
def obtenerEmbedding(texto):
    try:
        # Preparar el cuerpo de la solicitud para Titan Embeddings
        request_body = {
            "inputText": texto
        }

        # Llamar a Titan Text Embeddings
        response = bedrock_client.invoke_model(
            modelId=TITAN_EMBEDDINGS_MODEL_ID,
            body=json.dumps(request_body)
        )

        # Procesar la respuesta
        response_body = json.loads(response['body'].read())
        embedding = np.array(response_body['embedding'])
        return embedding

    except Exception as e:
        print(f"Error obteniendo embedding con Titan: {e}")
        # Retornar un embedding vacío en caso de error
        return np.zeros(1536)  # Titan embeddings tienen 1536 dimensiones

# Variables para el health check
start_time = time.time()
app_version = "1.0.0"

# Rutas de la API
@app.get("/health", status_code=status.HTTP_200_OK)
async def health():
    """
    Basic health check endpoint that returns a simple status.
    Used by container orchestration systems to check if the service is running.
    """
    return {
        "status": "ok",
        "bedrock_connected": bedrock_connected,
        "version": app_version,
        "timestamp": datetime.now().isoformat()
    }

@app.get("/", response_class=HTMLResponse)
async def read_root():
    # Servir el archivo HTML directamente
    try:
        with open("templates/index.html", "r", encoding="utf-8") as f:
            html_content = f.read()
        return HTMLResponse(content=html_content)
    except FileNotFoundError:
        return HTMLResponse(content="<h1>Error: Template not found</h1>", status_code=404)

@app.post("/generate-description")
async def generate_description(file: UploadFile = File(...), empresa: str = Form(...)):
    """Genera descripción automática de la imagen sin guardar el producto"""
    try:
        # Validar formato de imagen
        if not file.content_type.startswith('image/'):
            return JSONResponse(
                status_code=400,
                content={"error": "El archivo debe ser una imagen válida"}
            )

        # Leer la imagen
        image_bytes = await file.read()
        
        # Validar tamaño de imagen (máximo 10MB)
        if len(image_bytes) > 10 * 1024 * 1024:
            return JSONResponse(
                status_code=400,
                content={"error": "La imagen es demasiado grande. Máximo 10MB"}
            )

        # Obtener la descripción de la imagen usando Nova Lite (modelo más barato)
        descripcion = describeImagen(image_bytes)
        
        if descripcion == "Error al describir la imagen":
            return JSONResponse(
                status_code=500,
                content={"error": "No se pudo analizar la imagen. Intenta con otra imagen"}
            )

        # Convertir la imagen a base64 para mostrarla en el frontend
        encoded_image = base64.b64encode(image_bytes).decode("utf-8")

        return {
            "descripcion": descripcion,
            "imagen_base64": encoded_image,
            "empresa": empresa,
            "nombre": file.filename
        }

    except Exception as e:
        print(f"Error en generate_description: {e}")
        return JSONResponse(
            status_code=500,
            content={"error": f"Error interno del servidor: {str(e)}"}
        )

@app.post("/upload-image")
async def upload_image(file: UploadFile = File(...), empresa: str = Form(...), descripcion: str = Form(...)):
    """Guarda el producto con la descripción confirmada por el usuario"""
    try:
        # Leer la imagen
        image_bytes = await file.read()

        # Convertir la imagen a base64 para mostrarla en el frontend
        encoded_image = base64.b64encode(image_bytes).decode("utf-8")

        # Obtener el embedding de la descripción
        embedding = obtenerEmbedding(descripcion)

        # Guardar la información en la base de datos en memoria
        imagen_info = {
            "id": len(imagenes_db),
            "nombre": file.filename,
            "empresa": empresa,
            "descripcion": descripcion,
            "imagen_base64": encoded_image,
            "embedding": embedding
        }
        imagenes_db.append(imagen_info)

        # Devolver la información del producto
        return {
            "id": imagen_info["id"],
            "nombre": imagen_info["nombre"],
            "empresa": imagen_info["empresa"],
            "descripcion": imagen_info["descripcion"],
            "imagen_base64": imagen_info["imagen_base64"]
        }

    except Exception as e:
        print(f"Error en upload_image: {e}")
        return JSONResponse(
            status_code=500,
            content={"error": f"Error al guardar el producto: {str(e)}"}
        )

@app.post("/search")
async def search(request: Request):
    # Obtener el cuerpo de la solicitud como JSON
    data = await request.json()
    query = data.get("query", "")

    # Procesar la consulta (simplificado, sin manejo de precios)
    texto_limpio = query.strip()

    # Obtener el embedding de la consulta
    query_embedding = obtenerEmbedding(texto_limpio)

    # Calcular distancias con todos los productos
    resultados = []
    for img in imagenes_db:
        distancia = np.linalg.norm(query_embedding - img["embedding"])
        resultados.append({
            "id": img["id"],
            "nombre": img["nombre"],
            "empresa": img["empresa"],
            "descripcion": img["descripcion"],
            "imagen_base64": img["imagen_base64"],
            "distancia": float(distancia)
        })

    # Ordenar por distancia (menor a mayor)
    resultados.sort(key=lambda x: x["distancia"])

    return {
        "query_original": query,
        "query_limpia": texto_limpio,
        "resultados": resultados
    }

@app.get("/images")
async def get_images():
    # Devolver todos los productos almacenados
    return [
        {
            "id": img["id"],
            "nombre": img["nombre"],
            "empresa": img["empresa"],
            "descripcion": img["descripcion"],
            "imagen_base64": img["imagen_base64"]
        } for img in imagenes_db
    ]

if __name__ == "__main__":
    uvicorn.run("simple_app:app", host="0.0.0.0", port=8000, reload=True)
