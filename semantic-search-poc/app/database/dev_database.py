"""
Development Database Connection Module

This module provides connection and data extraction capabilities for the development database
(dev-dropi-colombia-cluster) to safely extract product and photo data for migration to the
semantic search database.
"""

import os
import boto3
from typing import List, Dict, Optional, Tuple
from sqlalchemy import create_engine, text, MetaData
from sqlalchemy.orm import sessionmaker
from sqlalchemy.exc import SQLAlchemyError
import logging
from dataclasses import dataclass
from dotenv import load_dotenv

load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ProductData:
    """Data class for product information from development database"""
    id: int
    name: str
    description: Optional[str]
    sale_price: Optional[float]
    sku: Optional[str]
    dropi_app_description: Optional[str]

@dataclass
class ProductPhotoData:
    """Data class for product photo information from development database"""
    id: int
    url: Optional[str]
    product_id: int
    url_s3: Optional[str]

@dataclass
class ProductWithPhotos:
    """Combined product and photos data"""
    product: ProductData
    photos: List[ProductPhotoData]

class DevDatabaseManager:
    """
    Database manager for the development database connection and data extraction
    """

    def __init__(self):
        # Get RDS credentials using AWS profile DEV
        self.dev_database_url = self._get_dev_database_url()

        # Create engine for development database
        self.dev_engine = create_engine(
            self.dev_database_url,
            pool_pre_ping=True,
            pool_recycle=300,
            echo=False  # Set to True for SQL debugging
        )

        # Create session factory
        self.DevSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.dev_engine)

        logger.info("Development database manager initialized")

    def _get_dev_database_url(self) -> str:
        """
        Get the development database URL with proper credentials
        """
        # Check for environment variables first
        dev_db_url = os.getenv('DEV_DATABASE_URL')
        if dev_db_url:
            logger.info("Using DEV_DATABASE_URL from environment")
            return dev_db_url

        # Check for individual components
        db_host = os.getenv('DEV_DB_HOST', 'dev-dropi-colombia-cluster.cluster-cpys4iioq9iw.us-east-2.rds.amazonaws.com')
        db_port = os.getenv('DEV_DB_PORT', '5432')
        db_name = os.getenv('DEV_DB_NAME', 'dropi_colombia')
        db_user = os.getenv('DEV_DB_USER', 'postgres')
        db_password = os.getenv('DEV_DB_PASSWORD', '')

        if db_password:
            database_url = f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"
            logger.info(f"Using configured credentials for user: {db_user}")
        else:
            # Try without password (for IAM auth or other auth methods)
            database_url = f"postgresql://{db_user}@{db_host}:{db_port}/{db_name}"
            logger.info(f"Attempting connection without password for user: {db_user}")

        return database_url
    
    def test_connection(self) -> bool:
        """
        Test connection to the development database
        
        Returns:
            bool: True if connection successful, False otherwise
        """
        try:
            with self.dev_engine.connect() as conn:
                result = conn.execute(text("SELECT 1"))
                result.fetchone()
                logger.info("✅ Development database connection successful")
                return True
        except Exception as e:
            logger.error(f"❌ Development database connection failed: {e}")
            return False
    
    def get_products_count(self) -> int:
        """
        Get total count of products in the development database
        
        Returns:
            int: Total number of products
        """
        try:
            with self.dev_engine.connect() as conn:
                result = conn.execute(text("SELECT COUNT(*) FROM products"))
                count = result.scalar()
                logger.info(f"Total products in development database: {count}")
                return count
        except Exception as e:
            logger.error(f"Error getting products count: {e}")
            return 0
    
    def get_products_with_photos_count(self) -> int:
        """
        Get count of products that have associated photos
        
        Returns:
            int: Number of products with photos
        """
        try:
            with self.dev_engine.connect() as conn:
                query = text("""
                    SELECT COUNT(DISTINCT p.id) 
                    FROM products p 
                    INNER JOIN product_photos pp ON p.id = pp.product_id
                    WHERE pp.url_s3 IS NOT NULL AND pp.url_s3 != ''
                """)
                result = conn.execute(query)
                count = result.scalar()
                logger.info(f"Products with S3 photos: {count}")
                return count
        except Exception as e:
            logger.error(f"Error getting products with photos count: {e}")
            return 0
    
    def extract_products_batch(self, offset: int = 0, limit: int = 100) -> List[ProductWithPhotos]:
        """
        Extract a batch of products with their associated photos
        
        Args:
            offset: Starting offset for pagination
            limit: Maximum number of products to return
            
        Returns:
            List[ProductWithPhotos]: List of products with their photos
        """
        try:
            with self.dev_engine.connect() as conn:
                # Query to get products with their photos
                query = text("""
                    SELECT 
                        p.id as product_id,
                        p.name as product_name,
                        p.description as product_description,
                        p.sale_price as product_sale_price,
                        p.sku as product_sku,
                        p.dropi_app_description as product_dropi_app_description,
                        pp.id as photo_id,
                        pp.url as photo_url,
                        pp.url_s3 as photo_url_s3
                    FROM products p
                    LEFT JOIN product_photos pp ON p.id = pp.product_id
                    WHERE p.id IS NOT NULL
                    ORDER BY p.id, pp.id
                    OFFSET :offset LIMIT :limit_with_photos
                """)
                
                # Use a larger limit to account for multiple photos per product
                result = conn.execute(query, {"offset": offset, "limit_with_photos": limit * 10})
                rows = result.fetchall()
                
                # Group results by product
                products_dict = {}
                for row in rows:
                    product_id = row.product_id
                    
                    if product_id not in products_dict:
                        products_dict[product_id] = ProductWithPhotos(
                            product=ProductData(
                                id=row.product_id,
                                name=row.product_name or "",
                                description=row.product_description,
                                sale_price=float(row.product_sale_price) if row.product_sale_price else None,
                                sku=row.product_sku,
                                dropi_app_description=row.product_dropi_app_description
                            ),
                            photos=[]
                        )
                    
                    # Add photo if it exists
                    if row.photo_id:
                        photo = ProductPhotoData(
                            id=row.photo_id,
                            url=row.photo_url,
                            product_id=row.product_id,
                            url_s3=row.photo_url_s3
                        )
                        products_dict[product_id].photos.append(photo)
                
                # Convert to list and limit to requested number of products
                products_list = list(products_dict.values())[:limit]
                
                logger.info(f"Extracted {len(products_list)} products with photos from offset {offset}")
                return products_list
                
        except Exception as e:
            logger.error(f"Error extracting products batch: {e}")
            return []
    
    def get_product_by_id(self, product_id: int) -> Optional[ProductWithPhotos]:
        """
        Get a specific product by ID with its photos
        
        Args:
            product_id: The product ID to retrieve
            
        Returns:
            Optional[ProductWithPhotos]: Product with photos or None if not found
        """
        try:
            products = self.extract_products_batch(offset=0, limit=1000)  # Get a large batch
            for product in products:
                if product.product.id == product_id:
                    return product
            return None
        except Exception as e:
            logger.error(f"Error getting product by ID {product_id}: {e}")
            return None
    
    def get_database_stats(self) -> Dict[str, int]:
        """
        Get comprehensive statistics about the development database
        
        Returns:
            Dict[str, int]: Database statistics
        """
        stats = {}
        try:
            with self.dev_engine.connect() as conn:
                # Total products
                result = conn.execute(text("SELECT COUNT(*) FROM products"))
                stats['total_products'] = result.scalar()
                
                # Total photos
                result = conn.execute(text("SELECT COUNT(*) FROM product_photos"))
                stats['total_photos'] = result.scalar()
                
                # Products with S3 URLs
                result = conn.execute(text("""
                    SELECT COUNT(DISTINCT product_id) 
                    FROM product_photos 
                    WHERE url_s3 IS NOT NULL AND url_s3 != ''
                """))
                stats['products_with_s3_photos'] = result.scalar()
                
                # Products with descriptions
                result = conn.execute(text("""
                    SELECT COUNT(*) FROM products 
                    WHERE description IS NOT NULL AND description != ''
                """))
                stats['products_with_descriptions'] = result.scalar()
                
                # Products with dropi_app_description
                result = conn.execute(text("""
                    SELECT COUNT(*) FROM products 
                    WHERE dropi_app_description IS NOT NULL AND dropi_app_description != ''
                """))
                stats['products_with_dropi_descriptions'] = result.scalar()
                
                logger.info(f"Database stats: {stats}")
                return stats
                
        except Exception as e:
            logger.error(f"Error getting database stats: {e}")
            return stats
    
    def close(self):
        """Close the database connection"""
        if hasattr(self, 'dev_engine'):
            self.dev_engine.dispose()
            logger.info("Development database connection closed")

# Global instance
dev_db_manager = DevDatabaseManager()
