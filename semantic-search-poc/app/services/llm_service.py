import os
import json
import base64
from typing import Optional, Dict
import boto3
from botocore.config import Config
from botocore.exceptions import Client<PERSON>rror
from dotenv import load_dotenv

load_dotenv()

class LLMService:
    def __init__(self):
        # AWS configuration
        aws_region = os.getenv("AWS_REGION", "us-east-2")

        # Configure retry settings for robustness
        retry_config = Config(
            region_name=aws_region,
            retries={
                "max_attempts": 10,
                "mode": "standard",
            },
        )

        # Use Nova Lite - the cheapest multimodal model available in Bedrock for vision tasks
        # Nova Micro is text-only, so we use Nova Lite for image analysis
        # Must use inference profile for Nova models (foundation model not supported for on-demand)
        self.model_id = "us.amazon.nova-lite-v1:0"
        self.aws_region = aws_region

        # Always use IAM role in cloud deployment
        # Explicitly set region to avoid boto3 defaulting to us-east-1
        self.client = boto3.client(
            service_name='bedrock-runtime',
            region_name=aws_region,
            config=retry_config
        )

        # Debug: Print the region being used
        print(f"LLM Service initialized with region: {aws_region}")
        print(f"Using model ID: {self.model_id}")
    
    def generate_image_description(self, image_bytes: Optional[bytes] = None,
                                 existing_text: Optional[str] = None) -> Optional[str]:
        """
        Generate a detailed product description using Amazon Nova Lite (cheapest multimodal model)

        Args:
            image_bytes: Image file bytes for vision analysis
            existing_text: Existing text description to enhance

        Returns:
            Generated description or None if error
        """
        try:
            if image_bytes:
                # Generate description from image using Nova Micro vision capabilities
                encoded_image = base64.b64encode(image_bytes).decode('utf-8')

                user_prompt = """Describe este producto en español con detalles específicos sobre:
                - Tipo de producto
                - Colores principales
                - Material aparente
                - Estilo o características distintivas
                - Uso o función

                Responde solo con la descripción del producto, sin explicaciones adicionales."""

                # Prepare request body for Nova Lite with image
                request_body = {
                    "messages": [
                        {
                            "role": "user",
                            "content": [
                                {
                                    "image": {
                                        "format": "jpeg",
                                        "source": {
                                            "bytes": encoded_image
                                        }
                                    }
                                },
                                {
                                    "text": user_prompt
                                }
                            ]
                        }
                    ],
                    "inferenceConfig": {
                        "maxTokens": 300,
                        "temperature": 0.2
                    }
                }
            elif existing_text:
                # Enhance existing text description
                user_prompt = f"""
                Dado el siguiente texto de descripción de un producto, genera una descripción más detallada
                y completa en español que sea útil para búsqueda semántica. Incluye características como:
                - Color, material, estilo
                - Categoría del producto
                - Características distintivas
                - Uso o propósito

                Texto original: "{existing_text}"

                Responde únicamente con la descripción mejorada, sin explicaciones adicionales.
                """

                # Prepare request body for Nova Lite text-only
                request_body = {
                    "messages": [
                        {
                            "role": "user",
                            "content": [
                                {
                                    "text": f"Eres un experto en descripción de productos para e-commerce. {user_prompt}"
                                }
                            ]
                        }
                    ],
                    "inferenceConfig": {
                        "maxTokens": 200,
                        "temperature": 0.3
                    }
                }
            else:
                return "Error: Se requiere imagen o texto para generar descripción"

            # Call Nova Lite using invoke_model
            response = self.client.invoke_model(
                modelId=self.model_id,
                body=json.dumps(request_body)
            )

            # Parse response
            response_body = json.loads(response['body'].read())
            description = response_body['output']['message']['content'][0]['text'].strip()

            # Validate that the description is not empty
            if not description or len(description) < 10:
                return "Producto visible en la imagen"

            return description

        except ClientError as e:
            error_code = e.response['Error']['Code']
            print(f"Error de AWS Bedrock: {error_code} - {e}")
            if error_code == 'ValidationException':
                return "Error: formato de imagen no válido"
            elif error_code == 'ThrottlingException':
                return "Error: servicio temporalmente no disponible"
            else:
                return "Error al analizar la imagen"
        except Exception as e:
            print(f"Error generating image description with Nova Lite: {e}")
            return None
    
    def clean_search_query(self, query: str) -> tuple[str, str]:
        """
        Clean search query and extract price information using Nova Lite

        Args:
            query: Original search query

        Returns:
            Tuple of (cleaned_query, price_info)
        """
        try:
            # Extract price information using invoke_model
            price_request = {
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "text": f"""
                                Dado el siguiente texto de búsqueda, extrae ÚNICAMENTE información de precios
                                (precio mínimo y máximo si están mencionados). Si no hay información de precios,
                                responde "Sin información de precios".

                                Texto: "{query}"
                                """
                            }
                        ]
                    }
                ],
                "inferenceConfig": {
                    "max_new_tokens": 50,
                    "temperature": 0.1
                }
            }

            price_response = self.client.invoke_model(
                modelId=self.model_id,
                body=json.dumps(price_request),
                contentType="application/json"
            )

            price_body = json.loads(price_response['body'].read())
            price_info = price_body['output']['message']['content'][0]['text'].strip()

            # Enhanced search query processing for better semantic matching
            clean_request = {
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "text": f"""
                                Eres un experto en búsqueda semántica de productos. Tu tarea es transformar la consulta de búsqueda del usuario en una descripción optimizada que incluya sinónimos y términos relacionados para mejorar la búsqueda semántica.

                                INSTRUCCIONES:
                                1. Elimina toda referencia a precios de la consulta original
                                2. Identifica el tipo de producto principal que busca el usuario
                                3. Incluye sinónimos y términos relacionados en español para ese tipo de producto
                                4. Incluye variaciones comunes de cómo se puede describir el producto
                                5. Mantén el contexto y características específicas mencionadas por el usuario

                                EJEMPLOS DE TRANSFORMACIÓN:
                                - "loción" → "loción perfume fragancia colonia agua de tocador producto cosmético aromático"
                                - "crema facial" → "crema facial hidratante moisturizer cuidado piel cosmético facial tratamiento rostro"
                                - "champú" → "champú shampoo producto capilar limpieza cabello cuidado pelo"
                                - "maquillaje" → "maquillaje cosmético belleza makeup producto facial colorete base labial"
                                - "desodorante" → "desodorante antitranspirante producto higiene personal cuidado corporal"

                                CONSULTA ORIGINAL: "{query}"

                                Responde únicamente con la descripción optimizada, incluyendo el término original y sus sinónimos/términos relacionados, sin explicaciones adicionales.
                                """
                            }
                        ]
                    }
                ],
                "inferenceConfig": {
                    "max_new_tokens": 150,
                    "temperature": 0.2
                }
            }

            clean_response = self.client.invoke_model(
                modelId=self.model_id,
                body=json.dumps(clean_request),
                contentType="application/json"
            )

            clean_body = json.loads(clean_response['body'].read())
            clean_query = clean_body['output']['message']['content'][0]['text'].strip()

            return clean_query, price_info

        except Exception as e:
            print(f"Error cleaning search query with Nova Lite: {e}")
            return query, "Error al procesar precios"
    
    def test_connection(self) -> bool:
        """
        Test connection to AWS Bedrock Nova Lite

        Returns:
            True if connection successful, False otherwise
        """
        try:
            test_request = {
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "text": "test"
                            }
                        ]
                    }
                ],
                "inferenceConfig": {
                    "max_new_tokens": 5
                }
            }

            response = self.client.invoke_model(
                modelId=self.model_id,
                body=json.dumps(test_request),
                contentType="application/json"
            )
            return True

        except Exception as e:
            print(f"AWS Bedrock Nova Lite connection test failed: {e}")
            return False

    def generate_enhanced_product_description(self,
                                            product_name: str,
                                            supplier_description: Optional[str] = None,
                                            dropi_description: Optional[str] = None,
                                            image_bytes: Optional[bytes] = None,
                                            price: Optional[float] = None) -> Optional[str]:
        """
        Generate an enhanced product description combining supplier info with LLM analysis

        Args:
            product_name: Name of the product
            supplier_description: Original supplier description
            dropi_description: Dropi app description if available
            image_bytes: Product image for visual analysis
            price: Product price for context

        Returns:
            Enhanced description or None if error
        """
        try:
            # Build context from available information
            context_parts = []

            if product_name:
                context_parts.append(f"Producto: {product_name}")

            if price:
                context_parts.append(f"Precio: ${price:,.0f}")

            if supplier_description:
                context_parts.append(f"Descripción del proveedor: {supplier_description}")

            if dropi_description:
                context_parts.append(f"Descripción Dropi: {dropi_description}")

            context_text = "\n".join(context_parts)

            # Enhanced prompt for product description
            enhanced_prompt = f"""Basándote en la siguiente información del producto, genera una descripción completa y detallada en español que incluya:

INFORMACIÓN DISPONIBLE:
{context_text}

GENERA UNA DESCRIPCIÓN QUE INCLUYA:
1. **Tipo de producto y categoría**
2. **Características físicas** (colores, materiales, texturas, dimensiones aparentes)
3. **Funcionalidad y uso** (para qué sirve, cómo se usa)
4. **Público objetivo** (hombres, mujeres, niños, unisex)
5. **Estilo y diseño** (moderno, clásico, deportivo, elegante, etc.)
6. **Características distintivas** (lo que lo hace especial o único)
7. **Ocasiones de uso** (casual, formal, deportivo, trabajo, etc.)

FORMATO DE RESPUESTA:
Escribe una descripción fluida y natural en español, sin usar viñetas ni numeración. Combina toda la información en párrafos coherentes que sean útiles para búsqueda semántica. Enfócate en características que ayuden a los usuarios a encontrar el producto mediante búsquedas descriptivas.

DESCRIPCIÓN MEJORADA:"""

            # Prepare request for text-only enhancement
            if image_bytes:
                # Use image + text analysis
                encoded_image = base64.b64encode(image_bytes).decode('utf-8')

                request_body = {
                    "messages": [
                        {
                            "role": "user",
                            "content": [
                                {
                                    "image": {
                                        "format": "jpeg",
                                        "source": {
                                            "bytes": encoded_image
                                        }
                                    }
                                },
                                {
                                    "text": enhanced_prompt
                                }
                            ]
                        }
                    ],
                    "inferenceConfig": {
                        "maxTokens": 500,
                        "temperature": 0.3
                    }
                }
            else:
                # Text-only analysis
                request_body = {
                    "messages": [
                        {
                            "role": "user",
                            "content": [
                                {
                                    "text": enhanced_prompt
                                }
                            ]
                        }
                    ],
                    "inferenceConfig": {
                        "maxTokens": 500,
                        "temperature": 0.3
                    }
                }

            # Call Nova Lite
            response = self.client.invoke_model(
                modelId=self.model_id,
                body=json.dumps(request_body),
                contentType="application/json"
            )

            # Parse response
            response_body = json.loads(response['body'].read())
            enhanced_description = response_body['output']['message']['content'][0]['text'].strip()

            # Clean up the response
            enhanced_description = enhanced_description.replace("DESCRIPCIÓN MEJORADA:", "").strip()

            return enhanced_description

        except Exception as e:
            print(f"Error generating enhanced product description: {e}")
            return None

    def extract_product_characteristics(self, description: str) -> Optional[Dict[str, str]]:
        """
        Extract structured characteristics from a product description

        Args:
            description: Product description to analyze

        Returns:
            Dictionary with extracted characteristics or None if error
        """
        try:
            analysis_prompt = f"""Analiza la siguiente descripción de producto y extrae información estructurada:

DESCRIPCIÓN:
{description}

Extrae la siguiente información en formato JSON:
{{
    "categoria": "categoría principal del producto",
    "material": "material principal",
    "color_principal": "color dominante",
    "genero": "hombre/mujer/unisex/niños",
    "estilo": "estilo o tipo (casual, formal, deportivo, etc.)",
    "ocasion_uso": "cuándo se usa (diario, trabajo, deporte, etc.)",
    "caracteristicas_especiales": "características distintivas"
}}

Responde SOLO con el JSON, sin explicaciones adicionales."""

            request_body = {
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "text": analysis_prompt
                            }
                        ]
                    }
                ],
                "inferenceConfig": {
                    "maxTokens": 300,
                    "temperature": 0.1
                }
            }

            response = self.client.invoke_model(
                modelId=self.model_id,
                body=json.dumps(request_body),
                contentType="application/json"
            )

            response_body = json.loads(response['body'].read())
            json_text = response_body['output']['message']['content'][0]['text'].strip()

            # Try to parse the JSON response
            try:
                characteristics = json.loads(json_text)
                return characteristics
            except json.JSONDecodeError:
                # If JSON parsing fails, return None
                print(f"Failed to parse characteristics JSON: {json_text}")
                return None

        except Exception as e:
            print(f"Error extracting product characteristics: {e}")
            return None

# Global LLM service instance
llm_service = LLMService()