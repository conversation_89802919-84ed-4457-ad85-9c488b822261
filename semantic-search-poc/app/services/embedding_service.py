import os
import json
import numpy as np
from typing import Optional
import boto3
from botocore.config import Config
from dotenv import load_dotenv

load_dotenv()

class EmbeddingService:
    def __init__(self):
        # AWS configuration
        aws_region = os.getenv("AWS_REGION", "us-east-2")

        # Configure retry settings for robustness
        retry_config = Config(
            region_name=aws_region,
            retries={
                "max_attempts": 10,
                "mode": "standard",
            },
        )

        # Always use IAM role in cloud deployment
        self.client = boto3.client(
            service_name='bedrock-runtime',
            region_name=aws_region,
            config=retry_config
        )

        # Use Titan Text Embeddings V2 model (supports 256, 512, 1024 dimensions)
        self.model_id = "amazon.titan-embed-text-v2:0"
        self.dimensions = 1024  # Maximum supported by Titan V2
    
    def get_embedding(self, text: str) -> Optional[np.ndarray]:
        """
        Generate embedding for the given text using Amazon Titan Text Embeddings V2

        Args:
            text: Input text to generate embedding for

        Returns:
            numpy array of embedding vector (1536 dimensions) or None if error
        """
        try:
            # Clean and prepare text
            text = text.replace("\n", " ").strip()

            if not text:
                print("Warning: Empty text provided for embedding")
                return np.zeros(self.dimensions)

            # Prepare request body for Titan Embeddings
            body = json.dumps({
                "inputText": text,
                "dimensions": self.dimensions,
                "normalize": True  # Normalize embeddings for better similarity search
            })

            # Call Bedrock to get embedding
            response = self.client.invoke_model(
                body=body,
                modelId=self.model_id,
                accept="application/json",
                contentType="application/json"
            )

            # Parse response
            response_body = json.loads(response.get('body').read())
            embedding = np.array(response_body['embedding'])

            return embedding

        except Exception as e:
            print(f"Error generating embedding with Titan: {e}")
            return None
    
    def get_embeddings_batch(self, texts: list) -> Optional[list]:
        """
        Generate embeddings for multiple texts (processed individually for Titan)

        Args:
            texts: List of texts to generate embeddings for

        Returns:
            List of numpy arrays or None if error
        """
        try:
            if not texts:
                return []

            embeddings = []
            for text in texts:
                embedding = self.get_embedding(text)
                if embedding is not None:
                    embeddings.append(embedding)
                else:
                    # Return None if any embedding fails
                    return None

            return embeddings

        except Exception as e:
            print(f"Error generating batch embeddings: {e}")
            return None
    
    def test_connection(self) -> bool:
        """
        Test connection to AWS Bedrock

        Returns:
            True if connection successful, False otherwise
        """
        try:
            # Test with a simple text
            test_embedding = self.get_embedding("test")
            return test_embedding is not None

        except Exception as e:
            print(f"AWS Bedrock connection test failed: {e}")
            return False

# Global embedding service instance
embedding_service = EmbeddingService()