import os
import uuid
import shutil
from typing import Op<PERSON>, BinaryIO
from abc import ABC, abstractmethod
from pathlib import Path
import boto3
from botocore.exceptions import ClientError
from dotenv import load_dotenv

load_dotenv()

class StorageService(ABC):
    """Abstract base class for storage services"""
    
    @abstractmethod
    async def upload_file(self, file_content: BinaryIO, filename: str, content_type: str) -> str:
        """Upload a file and return its URL"""
        pass
    
    @abstractmethod
    async def delete_file(self, file_url: str) -> bool:
        """Delete a file by its URL"""
        pass
    
    @abstractmethod
    def get_file_url(self, file_path: str) -> str:
        """Get the public URL for a file"""
        pass

class LocalStorageService(StorageService):
    """Local file storage service for development and testing"""
    
    def __init__(self, upload_dir: str = "uploads", base_url: str = "http://localhost:8000"):
        self.upload_dir = Path(upload_dir)
        self.base_url = base_url.rstrip('/')
        
        # Create upload directory if it doesn't exist
        self.upload_dir.mkdir(exist_ok=True)
        
        # Create subdirectories for organization
        (self.upload_dir / "images").mkdir(exist_ok=True)
        (self.upload_dir / "temp").mkdir(exist_ok=True)
    
    async def upload_file(self, file_content: BinaryIO, filename: str, content_type: str) -> str:
        """
        Upload file to local storage
        
        Args:
            file_content: File content as binary stream
            filename: Original filename
            content_type: MIME type of the file
            
        Returns:
            Public URL of the uploaded file
        """
        try:
            # Generate unique filename to avoid conflicts
            file_extension = Path(filename).suffix.lower()
            unique_filename = f"{uuid.uuid4()}{file_extension}"
            
            # Determine subdirectory based on content type
            if content_type.startswith('image/'):
                subdir = "images"
            else:
                subdir = "temp"
            
            file_path = self.upload_dir / subdir / unique_filename
            
            # Save file to local storage
            with open(file_path, 'wb') as f:
                shutil.copyfileobj(file_content, f)
            
            # Return public URL
            return self.get_file_url(f"{subdir}/{unique_filename}")
            
        except Exception as e:
            raise Exception(f"Failed to upload file to local storage: {str(e)}")
    
    async def delete_file(self, file_url: str) -> bool:
        """
        Delete file from local storage
        
        Args:
            file_url: Public URL of the file to delete
            
        Returns:
            True if file was deleted successfully
        """
        try:
            # Extract relative path from URL
            relative_path = file_url.replace(f"{self.base_url}/uploads/", "")
            file_path = self.upload_dir / relative_path
            
            if file_path.exists():
                file_path.unlink()
                return True
            return False
            
        except Exception as e:
            print(f"Failed to delete file from local storage: {str(e)}")
            return False
    
    def get_file_url(self, file_path: str) -> str:
        """
        Get public URL for a file
        
        Args:
            file_path: Relative path to the file
            
        Returns:
            Public URL
        """
        return f"{self.base_url}/uploads/{file_path}"

class S3StorageService(StorageService):
    """AWS S3 storage service for production"""
    
    def __init__(self, bucket_name: str, region: str = "us-east-1", aws_profile: str = None):
        self.bucket_name = bucket_name
        self.region = region

        # Initialize S3 client
        # In Lambda, use IAM role instead of profile
        if aws_profile and not os.getenv("AWS_LAMBDA_FUNCTION_NAME"):
            session = boto3.Session(profile_name=aws_profile)
            self.s3_client = session.client('s3', region_name=region)
        else:
            self.s3_client = boto3.client('s3', region_name=region)
    
    async def upload_file(self, file_content: BinaryIO, filename: str, content_type: str) -> str:
        """
        Upload file to S3
        
        Args:
            file_content: File content as binary stream
            filename: Original filename
            content_type: MIME type of the file
            
        Returns:
            Public URL of the uploaded file
        """
        try:
            # Generate unique filename
            file_extension = Path(filename).suffix.lower()
            unique_filename = f"{uuid.uuid4()}{file_extension}"
            
            # Determine S3 key based on content type
            if content_type.startswith('image/'):
                s3_key = f"images/{unique_filename}"
            else:
                s3_key = f"files/{unique_filename}"
            
            # Upload to S3
            self.s3_client.upload_fileobj(
                file_content,
                self.bucket_name,
                s3_key,
                ExtraArgs={
                    'ContentType': content_type
                    # Note: Public access is handled by bucket policy, not ACLs
                }
            )
            
            # Return public URL
            return self.get_file_url(s3_key)
            
        except ClientError as e:
            raise Exception(f"Failed to upload file to S3: {str(e)}")
    
    async def delete_file(self, file_url: str) -> bool:
        """
        Delete file from S3
        
        Args:
            file_url: Public URL of the file to delete
            
        Returns:
            True if file was deleted successfully
        """
        try:
            # Extract S3 key from URL
            s3_key = file_url.split(f"{self.bucket_name}.s3.amazonaws.com/")[-1]
            
            self.s3_client.delete_object(Bucket=self.bucket_name, Key=s3_key)
            return True
            
        except ClientError as e:
            print(f"Failed to delete file from S3: {str(e)}")
            return False
    
    def get_file_url(self, s3_key: str) -> str:
        """
        Get public URL for an S3 object
        
        Args:
            s3_key: S3 object key
            
        Returns:
            Public URL
        """
        return f"https://{self.bucket_name}.s3.amazonaws.com/{s3_key}"

class StorageServiceFactory:
    """Factory class to create appropriate storage service based on configuration"""
    
    @staticmethod
    def create_storage_service() -> StorageService:
        """
        Create storage service based on environment configuration
        
        Returns:
            Configured storage service instance
        """
        storage_type = os.getenv("STORAGE_TYPE", "local").lower()
        
        if storage_type == "s3":
            bucket_name = os.getenv("S3_BUCKET_NAME")
            region = os.getenv("AWS_REGION", "us-east-1")
            aws_profile = os.getenv("AWS_PROFILE")
            
            if not bucket_name:
                raise ValueError("S3_BUCKET_NAME environment variable is required for S3 storage")
            
            return S3StorageService(bucket_name, region, aws_profile)
        
        elif storage_type == "local":
            upload_dir = os.getenv("UPLOAD_DIR", "uploads")
            base_url = os.getenv("BASE_URL", "http://localhost:8000")

            # In Lambda, use /tmp directory for temporary storage
            if os.getenv("AWS_LAMBDA_FUNCTION_NAME"):
                upload_dir = "/tmp/uploads"

            return LocalStorageService(upload_dir, base_url)
        
        else:
            raise ValueError(f"Unsupported storage type: {storage_type}")

# Global storage service instance
storage_service = StorageServiceFactory.create_storage_service()
