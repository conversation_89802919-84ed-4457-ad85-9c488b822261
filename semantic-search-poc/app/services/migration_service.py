"""
Data Migration Service

This service handles the complete migration pipeline from development database
to semantic search database, including image processing, description enhancement,
and embedding generation.
"""

import os
import sys
import asyncio
import aiohttp
import logging
from typing import List, Dict, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from datetime import datetime
import json
import time
from pathlib import Path

# Add the app directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from database.dev_database import dev_db_manager, ProductWithPhotos, ProductData, ProductPhotoData
from services.embedding_service import embedding_service
from services.llm_service import llm_service
from utils.validation import data_validator, ValidationResult

# Import db_manager directly from the database module file
import importlib.util
import os
db_module_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'database.py')
spec = importlib.util.spec_from_file_location("database", db_module_path)
database_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(database_module)
db_manager = database_module.db_manager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class MigrationResult:
    """Result of migrating a single product"""
    product_id: int
    success: bool
    error_message: Optional[str] = None
    photos_processed: int = 0
    enhanced_description: Optional[str] = None
    embedding_generated: bool = False
    stored_in_db: bool = False
    processing_time_seconds: float = 0.0
    validation_errors: List[str] = None
    validation_warnings: List[str] = None

    def __post_init__(self):
        if self.validation_errors is None:
            self.validation_errors = []
        if self.validation_warnings is None:
            self.validation_warnings = []

@dataclass
class MigrationStats:
    """Overall migration statistics"""
    total_products: int = 0
    successful_products: int = 0
    failed_products: int = 0
    total_photos_processed: int = 0
    total_processing_time: float = 0.0
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    errors: List[str] = None
    
    def __post_init__(self):
        if self.errors is None:
            self.errors = []

class MigrationService:
    """
    Comprehensive service for migrating product data from development to semantic search database
    """
    
    def __init__(self, company_name: str = "dropi_colombia"):
        self.company_name = company_name
        self.dev_db = dev_db_manager
        self.target_db = db_manager
        self.embedding_service = embedding_service
        self.llm_service = llm_service
        
        # Migration configuration
        self.max_concurrent_downloads = 5
        self.max_retries = 3
        self.retry_delay = 2.0
        self.image_timeout = 30.0
        
        logger.info(f"Migration service initialized for company: {company_name}")

    def validate_migration_batch(self, products: List[ProductWithPhotos]) -> Dict[str, Any]:
        """
        Validate a batch of products before migration

        Args:
            products: List of products to validate

        Returns:
            Dictionary with validation results
        """
        logger.info(f"Validating batch of {len(products)} products...")

        validation_results = data_validator.validate_batch(products)

        # Add migration-specific validation
        migration_ready_count = 0
        for product in products:
            s3_photos = [photo for photo in product.photos if photo.url_s3]
            if s3_photos:
                migration_ready_count += 1

        validation_results["migration_ready_products"] = migration_ready_count
        validation_results["migration_ready_percent"] = (migration_ready_count / len(products)) * 100 if products else 0

        logger.info(f"Validation complete: {validation_results['valid_products']}/{validation_results['total_products']} valid, "
                   f"{migration_ready_count} ready for migration")

        return validation_results
    
    async def download_image_from_s3(self, s3_url: str) -> Optional[bytes]:
        """
        Download image from S3 URL with retry logic
        
        Args:
            s3_url: S3 URL to download from
            
        Returns:
            Image bytes or None if failed
        """
        if not s3_url or not s3_url.startswith('http'):
            return None
        
        for attempt in range(self.max_retries):
            try:
                timeout = aiohttp.ClientTimeout(total=self.image_timeout)
                async with aiohttp.ClientSession(timeout=timeout) as session:
                    async with session.get(s3_url) as response:
                        if response.status == 200:
                            content_type = response.headers.get('content-type', '')
                            if 'image' in content_type.lower():
                                image_bytes = await response.read()
                                logger.debug(f"Downloaded image from {s3_url} ({len(image_bytes)} bytes)")
                                return image_bytes
                            else:
                                logger.warning(f"URL {s3_url} is not an image (content-type: {content_type})")
                                return None
                        else:
                            logger.warning(f"Failed to download {s3_url}: HTTP {response.status}")
                            
            except asyncio.TimeoutError:
                logger.warning(f"Timeout downloading {s3_url} (attempt {attempt + 1})")
            except Exception as e:
                logger.warning(f"Error downloading {s3_url} (attempt {attempt + 1}): {e}")
            
            if attempt < self.max_retries - 1:
                await asyncio.sleep(self.retry_delay)
        
        logger.error(f"Failed to download image after {self.max_retries} attempts: {s3_url}")
        return None
    
    async def process_single_product(self, product: ProductWithPhotos) -> MigrationResult:
        """
        Process a single product through the complete migration pipeline with validation

        Args:
            product: Product with photos to process

        Returns:
            MigrationResult with processing details
        """
        start_time = time.time()
        result = MigrationResult(product_id=product.product.id, success=False)

        try:
            logger.info(f"Processing product {product.product.id}: {product.product.name}")

            # Step 1: Validate input data
            validation = data_validator.validate_product_with_photos(product)
            result.validation_errors = validation.errors
            result.validation_warnings = validation.warnings

            if not validation.is_valid:
                result.error_message = f"Validation failed: {'; '.join(validation.errors)}"
                logger.warning(f"Product {product.product.id} failed validation: {result.error_message}")
                return result

            if validation.warnings:
                logger.info(f"Product {product.product.id} validation warnings: {'; '.join(validation.warnings)}")

            # Step 2: Filter photos with S3 URLs
            s3_photos = [photo for photo in product.photos if photo.url_s3]
            if not s3_photos:
                result.error_message = "No S3 photos available"
                return result
            
            # Step 3: Download the first available image for LLM analysis
            image_bytes = None
            download_errors = []

            for photo in s3_photos:
                try:
                    image_bytes = await self.download_image_from_s3(photo.url_s3)
                    if image_bytes:
                        logger.debug(f"Successfully downloaded image for product {product.product.id}")
                        break
                except Exception as e:
                    download_errors.append(f"Photo {photo.id}: {str(e)}")
                    logger.warning(f"Failed to download image {photo.url_s3}: {e}")

            if download_errors and not image_bytes:
                logger.warning(f"All image downloads failed for product {product.product.id}: {download_errors}")

            # Step 4: Generate enhanced description
            try:
                enhanced_description = self.llm_service.generate_enhanced_product_description(
                    product_name=product.product.name,
                    supplier_description=product.product.description,
                    dropi_description=product.product.dropi_app_description,
                    image_bytes=image_bytes,
                    price=product.product.sale_price
                )
            except Exception as e:
                logger.error(f"LLM description generation failed for product {product.product.id}: {e}")
                enhanced_description = None

            if not enhanced_description:
                # Fallback to original descriptions
                descriptions = []
                if product.product.name:
                    descriptions.append(product.product.name)
                if product.product.description:
                    descriptions.append(product.product.description)
                if product.product.dropi_app_description:
                    descriptions.append(product.product.dropi_app_description)

                enhanced_description = " ".join(descriptions) if descriptions else "Producto sin descripción"
                logger.info(f"Using fallback description for product {product.product.id}")

            result.enhanced_description = enhanced_description

            # Step 5: Generate embedding
            try:
                embedding = self.embedding_service.get_embedding(enhanced_description)
            except Exception as e:
                logger.error(f"Embedding generation failed for product {product.product.id}: {e}")
                embedding = None

            if embedding is None:
                result.error_message = "Failed to generate embedding"
                return result

            # Step 6: Validate embedding
            embedding_validation = data_validator.validate_embedding(embedding)
            if not embedding_validation.is_valid:
                result.error_message = f"Invalid embedding: {'; '.join(embedding_validation.errors)}"
                return result

            result.embedding_generated = True
            
            # Step 7: Store in semantic search database for each photo with S3 URL
            photos_stored = 0
            storage_errors = []

            for photo in s3_photos:
                try:
                    # Validate photo data before storage
                    photo_validation = data_validator.validate_photo_data(photo)
                    if not photo_validation.is_valid:
                        storage_errors.append(f"Photo {photo.id} validation failed: {'; '.join(photo_validation.errors)}")
                        continue

                    success = self.target_db.insert_image(
                        company_name=self.company_name,
                        id_producto=product.product.id,
                        id_photo=photo.id,
                        url_photo=photo.url_s3,
                        texto=enhanced_description,
                        embedding=embedding
                    )

                    if success:
                        photos_stored += 1
                        logger.debug(f"Stored photo {photo.id} for product {product.product.id}")
                    else:
                        storage_errors.append(f"Photo {photo.id}: Database insert failed")
                        logger.warning(f"Failed to store photo {photo.id} for product {product.product.id}")

                except Exception as e:
                    storage_errors.append(f"Photo {photo.id}: {str(e)}")
                    logger.error(f"Error storing photo {photo.id}: {e}")

            result.photos_processed = photos_stored
            result.stored_in_db = photos_stored > 0
            result.success = photos_stored > 0

            if not result.success:
                error_details = "; ".join(storage_errors) if storage_errors else "Unknown storage error"
                result.error_message = f"Failed to store any photos in database: {error_details}"
            elif storage_errors:
                logger.warning(f"Some photos failed to store for product {product.product.id}: {storage_errors}")
            
        except Exception as e:
            logger.error(f"Error processing product {product.product.id}: {e}")
            result.error_message = str(e)
        
        finally:
            result.processing_time_seconds = time.time() - start_time
        
        return result
    
    async def migrate_products_batch(self, products: List[ProductWithPhotos]) -> Tuple[List[MigrationResult], MigrationStats]:
        """
        Migrate a batch of products with concurrent processing
        
        Args:
            products: List of products to migrate
            
        Returns:
            Tuple of (results, stats)
        """
        logger.info(f"Starting migration of {len(products)} products...")
        
        stats = MigrationStats(
            total_products=len(products),
            start_time=datetime.now()
        )
        
        # Create semaphore to limit concurrent processing
        semaphore = asyncio.Semaphore(self.max_concurrent_downloads)
        
        async def process_with_semaphore(product):
            async with semaphore:
                return await self.process_single_product(product)
        
        # Process all products concurrently
        tasks = [process_with_semaphore(product) for product in products]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results and update stats
        migration_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                error_result = MigrationResult(
                    product_id=products[i].product.id,
                    success=False,
                    error_message=str(result)
                )
                migration_results.append(error_result)
                stats.failed_products += 1
                stats.errors.append(f"Product {products[i].product.id}: {result}")
            else:
                migration_results.append(result)
                if result.success:
                    stats.successful_products += 1
                    stats.total_photos_processed += result.photos_processed
                else:
                    stats.failed_products += 1
                    if result.error_message:
                        stats.errors.append(f"Product {result.product_id}: {result.error_message}")
                
                stats.total_processing_time += result.processing_time_seconds
        
        stats.end_time = datetime.now()
        
        logger.info(f"Migration batch complete: {stats.successful_products}/{stats.total_products} successful")
        return migration_results, stats
    
    def ensure_company_table_exists(self) -> bool:
        """
        Ensure the company table exists in the semantic search database
        
        Returns:
            bool: True if table exists or was created successfully
        """
        try:
            success = self.target_db.create_company_table(self.company_name)
            if success:
                logger.info(f"✅ Company table for '{self.company_name}' is ready")
            else:
                logger.error(f"❌ Failed to create/verify company table for '{self.company_name}'")
            return success
        except Exception as e:
            logger.error(f"Error ensuring company table exists: {e}")
            return False
    
    def save_migration_report(self, results: List[MigrationResult], stats: MigrationStats, filename: str):
        """
        Save detailed migration report to file
        
        Args:
            results: List of migration results
            stats: Migration statistics
            filename: Output filename
        """
        report = {
            "migration_summary": asdict(stats),
            "detailed_results": [asdict(result) for result in results],
            "company_name": self.company_name,
            "migration_config": {
                "max_concurrent_downloads": self.max_concurrent_downloads,
                "max_retries": self.max_retries,
                "retry_delay": self.retry_delay,
                "image_timeout": self.image_timeout
            }
        }
        
        # Ensure output directory exists
        output_dir = Path(filename).parent
        output_dir.mkdir(parents=True, exist_ok=True)
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)
        
        logger.info(f"Migration report saved to {filename}")

# Global migration service instance
migration_service = MigrationService()
