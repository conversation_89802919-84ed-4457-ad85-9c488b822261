import os
from typing import List, Optional
from sqlalchemy import create_engine, text, MetaData, Table, Column, String, Integer, Text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.dialects.postgresql import ARRAY
import numpy as np
from dotenv import load_dotenv

load_dotenv()

# Database configuration
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://postgres:postgres@localhost:5432/semantic_search")

# SQLAlchemy setup
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

class DatabaseManager:
    def __init__(self):
        self.engine = engine
        self.metadata = MetaData()
    
    def get_db(self):
        """Get database session"""
        db = SessionLocal()
        try:
            yield db
        finally:
            db.close()
    
    def create_company_table(self, company_name: str) -> bool:
        """Create a table for a specific company/country"""
        try:
            table_name = f"imagenes_{company_name.lower()}"

            # Check if table already exists
            with self.engine.connect() as conn:
                table_exists_sql = """
                SELECT EXISTS (
                    SELECT FROM information_schema.tables
                    WHERE table_schema = 'public'
                    AND table_name = :table_name
                )
                """
                result = conn.execute(text(table_exists_sql), {"table_name": table_name})
                table_exists = result.scalar()

                if table_exists:
                    print(f"Table {table_name} already exists, skipping creation")
                    return True
            
            # Create table with vector column (1024 dimensions for Titan V2)
            create_table_sql = f"""
            CREATE TABLE {table_name} (
                empresa VARCHAR(255) NOT NULL,
                idProducto INT NOT NULL,
                idPhoto INT NOT NULL,
                urlPhoto TEXT,
                texto TEXT,
                embedding VECTOR(1024),
                PRIMARY KEY (empresa, idProducto, idPhoto)
            )
            """
            
            with self.engine.connect() as conn:
                conn.execute(text(create_table_sql))
                conn.commit()
            
            # Create vector index
            self.create_vector_index(company_name)
            
            # Recreate the unified view
            self.recreate_imagenes_view()
            
            return True
            
        except Exception as e:
            print(f"Error creating table for company {company_name}: {e}")
            return False
    
    def create_vector_index(self, company_name: str) -> bool:
        """Create vector index for a company table"""
        try:
            table_name = f"imagenes_{company_name.lower()}"
            index_name = f"idx_imagenes_{company_name.lower()}"
            
            # Drop index if exists
            with self.engine.connect() as conn:
                conn.execute(text(f"DROP INDEX IF EXISTS {index_name}"))
                conn.commit()
            
            # Create HNSW index for cosine similarity
            create_index_sql = f"""
            CREATE INDEX {index_name} ON {table_name} 
            USING HNSW (embedding vector_cosine_ops)
            """
            
            with self.engine.connect() as conn:
                conn.execute(text(create_index_sql))
                conn.commit()
            
            return True
            
        except Exception as e:
            print(f"Error creating vector index for company {company_name}: {e}")
            return False
    
    def get_existing_companies(self) -> List[str]:
        """Get list of existing company tables"""
        try:
            with self.engine.connect() as conn:
                result = conn.execute(text("""
                    SELECT table_name 
                    FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name LIKE 'imagenes_%'
                """))
                
                companies = []
                for row in result:
                    table_name = row[0]
                    if table_name.startswith('imagenes_'):
                        company = table_name[9:]  # Remove 'imagenes_' prefix
                        companies.append(company)
                
                return companies
                
        except Exception as e:
            print(f"Error getting existing companies: {e}")
            return []
    
    def recreate_imagenes_view(self) -> bool:
        """Recreate the unified IMAGENES view"""
        try:
            companies = self.get_existing_companies()
            
            if not companies:
                print("No company tables found, skipping view creation")
                return True
            
            # Drop existing view
            with self.engine.connect() as conn:
                conn.execute(text("DROP VIEW IF EXISTS IMAGENES"))
                conn.commit()
            
            # Create UNION ALL query for all company tables
            union_parts = []
            for company in companies:
                table_name = f"imagenes_{company.lower()}"
                union_parts.append(f"""
                    SELECT empresa, idProducto, idPhoto, urlPhoto, texto, embedding
                    FROM {table_name}
                """)
            
            create_view_sql = f"""
            CREATE VIEW IMAGENES AS
            {' UNION ALL '.join(union_parts)}
            """
            
            with self.engine.connect() as conn:
                conn.execute(text(create_view_sql))
                conn.commit()
            
            return True
            
        except Exception as e:
            print(f"Error recreating IMAGENES view: {e}")
            return False    
    def insert_image(self, company_name: str, id_producto: int, id_photo: int,
                    url_photo: Optional[str], texto: Optional[str], embedding: np.ndarray) -> bool:
        """Insert or update an image in the company table"""
        try:
            table_name = f"imagenes_{company_name.lower()}"
            
            # Convert numpy array to list for PostgreSQL
            embedding_list = embedding.tolist()
            
            # Delete existing record if it exists
            delete_sql = f"""
            DELETE FROM {table_name} 
            WHERE empresa = :empresa AND idProducto = :id_producto AND idPhoto = :id_photo
            """
            
            # Insert new record
            insert_sql = f"""
            INSERT INTO {table_name} (empresa, idProducto, idPhoto, urlPhoto, texto, embedding)
            VALUES (:empresa, :id_producto, :id_photo, :url_photo, :texto, :embedding)
            """
            
            with self.engine.connect() as conn:
                # Delete existing
                conn.execute(text(delete_sql), {
                    "empresa": company_name,
                    "id_producto": id_producto,
                    "id_photo": id_photo
                })
                
                # Insert new
                conn.execute(text(insert_sql), {
                    "empresa": company_name,
                    "id_producto": id_producto,
                    "id_photo": id_photo,
                    "url_photo": url_photo,
                    "texto": texto,
                    "embedding": embedding_list
                })
                
                conn.commit()
            
            return True
            
        except Exception as e:
            print(f"Error inserting image for company {company_name}: {e}")
            return False
    
    def search_similar_images(self, company_name: str, query_embedding: np.ndarray, limit: int = 10) -> List[dict]:
        """Search for similar images using vector similarity"""
        try:
            # Convert numpy array to pgvector format string
            embedding_list = query_embedding.tolist()
            embedding_str = '[' + ','.join(map(str, embedding_list)) + ']'

            # Use string formatting to avoid parameter binding issues with vector type
            search_sql = f"""
            SELECT
                empresa,
                idProducto,
                idPhoto,
                urlPhoto,
                texto,
                embedding <-> '{embedding_str}'::vector AS distancia_coseno
            FROM
                IMAGENES
            WHERE
                empresa = '{company_name}'
            ORDER BY
                distancia_coseno
            LIMIT {limit}
            """

            with self.engine.connect() as conn:
                result = conn.execute(text(search_sql))

                results = []
                for row in result:
                    results.append({
                        "empresa": row[0],
                        "idProducto": row[1],
                        "idPhoto": row[2],
                        "urlPhoto": row[3],
                        "texto": row[4],
                        "distancia_coseno": float(row[5])
                    })

                return results

        except Exception as e:
            print(f"Error searching similar images for company {company_name}: {e}")
            return []

    def search_similar_images_all_vendors(self, query_embedding: np.ndarray, limit: int = 10) -> List[dict]:
        """Search for similar images across ALL vendors using vector similarity"""
        try:
            # Convert numpy array to pgvector format string
            embedding_list = query_embedding.tolist()
            embedding_str = '[' + ','.join(map(str, embedding_list)) + ']'

            # Search across ALL companies using the unified IMAGENES view
            search_sql = f"""
            SELECT
                empresa,
                idProducto,
                idPhoto,
                urlPhoto,
                texto,
                embedding <-> '{embedding_str}'::vector AS distancia_coseno
            FROM
                IMAGENES
            ORDER BY
                distancia_coseno
            LIMIT {limit}
            """

            with self.engine.connect() as conn:
                result = conn.execute(text(search_sql))

                results = []
                for row in result:
                    results.append({
                        "empresa": row[0],
                        "idProducto": row[1],
                        "idPhoto": row[2],
                        "urlPhoto": row[3],
                        "texto": row[4],
                        "distancia_coseno": float(row[5])
                    })

                return results

        except Exception as e:
            print(f"Error searching similar images across all vendors: {e}")
            return []
    
    def get_all_products(self, company_name: str) -> List[dict]:
        """Get all products for a company"""
        try:
            table_name = f"imagenes_{company_name.lower()}"

            with self.engine.connect() as conn:
                # Check if table exists first
                table_exists_sql = """
                SELECT EXISTS (
                    SELECT FROM information_schema.tables
                    WHERE table_schema = 'public'
                    AND table_name = :table_name
                )
                """

                result = conn.execute(text(table_exists_sql), {"table_name": table_name.lower()})
                table_exists = result.scalar()

                if not table_exists:
                    return []  # Return empty list if table doesn't exist

                # Get all products for the company
                products_sql = f"""
                SELECT empresa, idProducto, idPhoto, urlPhoto, texto
                FROM {table_name}
                ORDER BY idProducto, idPhoto
                """
                result = conn.execute(text(products_sql))

                products = []
                for row in result:
                    products.append({
                        "empresa": row[0],
                        "idProducto": row[1],
                        "idPhoto": row[2],
                        "urlPhoto": row[3],
                        "texto": row[4]
                    })

                return products

        except Exception as e:
            print(f"Error getting all products for company {company_name}: {e}")
            return []  # Return empty list if error occurs

    def get_next_product_id(self, company_name: str) -> int:
        """Get the next available product ID for a company"""
        try:
            table_name = f"imagenes_{company_name.lower()}"

            with self.engine.connect() as conn:
                # Check if table exists first
                table_exists_sql = """
                SELECT EXISTS (
                    SELECT FROM information_schema.tables
                    WHERE table_schema = 'public'
                    AND table_name = :table_name
                )
                """

                result = conn.execute(text(table_exists_sql), {"table_name": table_name.lower()})
                table_exists = result.scalar()

                if not table_exists:
                    return 1  # Start with 1 if table doesn't exist

                # Get the next available product ID
                next_id_sql = f"SELECT COALESCE(MAX(idProducto), 0) + 1 FROM {table_name}"
                result = conn.execute(text(next_id_sql))
                next_id = result.scalar()

                return next_id if next_id is not None else 1

        except Exception as e:
            print(f"Error getting next product ID for company {company_name}: {e}")
            return 1  # Default to 1 if error occurs

    def test_connection(self) -> bool:
        """Test database connection"""
        try:
            with self.engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            return True
        except Exception as e:
            print(f"Database connection error: {e}")
            return False

# Global database manager instance
db_manager = DatabaseManager()