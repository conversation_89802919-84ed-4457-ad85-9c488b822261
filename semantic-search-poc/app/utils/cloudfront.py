"""
CloudFront URL Utilities

This module provides utilities for generating CloudFront URLs for serving images
efficiently instead of direct S3 URLs.
"""

import os
import logging
from typing import Optional
from urllib.parse import urljoin, quote
from dotenv import load_dotenv

load_dotenv()

logger = logging.getLogger(__name__)

class CloudFrontURLGenerator:
    """
    Generator for CloudFront URLs to serve images efficiently
    """
    
    def __init__(self):
        # Get CloudFront configuration from environment
        self.cloudfront_base_url = os.getenv('CLOUDFRONT_BASE_URL', 'https://d9kz1bfy19fz0.cloudfront.net')
        self.s3_bucket_name = os.getenv('S3_BUCKET_NAME', 'dropi-core-colombia-dev')
        
        # Ensure CloudFront URL ends with /
        if not self.cloudfront_base_url.endswith('/'):
            self.cloudfront_base_url += '/'
        
        logger.info(f"CloudFront URL generator initialized: {self.cloudfront_base_url}")
    
    def generate_cloudfront_url(self, s3_key: str) -> str:
        """
        Generate CloudFront URL from S3 key
        
        Args:
            s3_key: S3 object key (e.g., "colombia/products/129652/filename.jpg")
            
        Returns:
            CloudFront URL for the image
        """
        if not s3_key:
            logger.warning("Empty S3 key provided")
            return ""
        
        # Remove leading slash if present
        if s3_key.startswith('/'):
            s3_key = s3_key[1:]
        
        # URL encode the key to handle special characters
        encoded_key = quote(s3_key, safe='/')
        
        # Combine CloudFront base URL with the S3 key
        cloudfront_url = urljoin(self.cloudfront_base_url, encoded_key)
        
        logger.debug(f"Generated CloudFront URL: {s3_key} -> {cloudfront_url}")
        return cloudfront_url
    
    def convert_s3_url_to_cloudfront(self, s3_url: str) -> str:
        """
        Convert existing S3 URL to CloudFront URL
        
        Args:
            s3_url: Full S3 URL (e.g., "https://bucket.s3.region.amazonaws.com/key")
            
        Returns:
            CloudFront URL for the same object
        """
        if not s3_url:
            return ""
        
        try:
            # Extract S3 key from various S3 URL formats
            s3_key = self.extract_s3_key_from_url(s3_url)
            if s3_key:
                return self.generate_cloudfront_url(s3_key)
            else:
                logger.warning(f"Could not extract S3 key from URL: {s3_url}")
                return s3_url  # Return original URL as fallback
                
        except Exception as e:
            logger.error(f"Error converting S3 URL to CloudFront: {e}")
            return s3_url  # Return original URL as fallback
    
    def extract_s3_key_from_url(self, s3_url: str) -> Optional[str]:
        """
        Extract S3 key from various S3 URL formats
        
        Args:
            s3_url: S3 URL in various formats
            
        Returns:
            S3 key or None if extraction fails
        """
        if not s3_url:
            return None
        
        try:
            # Handle different S3 URL formats:
            # 1. https://bucket.s3.region.amazonaws.com/key
            # 2. https://s3.region.amazonaws.com/bucket/key
            # 3. s3://bucket/key
            
            if s3_url.startswith('s3://'):
                # Format: s3://bucket/key
                parts = s3_url[5:].split('/', 1)
                if len(parts) > 1:
                    return parts[1]
            
            elif 'amazonaws.com/' in s3_url:
                # Format: https://bucket.s3.region.amazonaws.com/key
                # or https://s3.region.amazonaws.com/bucket/key
                parts = s3_url.split('amazonaws.com/')
                if len(parts) > 1:
                    key_part = parts[1]
                    
                    # If URL contains bucket name in path, remove it
                    if key_part.startswith(f'{self.s3_bucket_name}/'):
                        key_part = key_part[len(f'{self.s3_bucket_name}/'):]
                    
                    return key_part
            
            # If it's already just a key (no protocol), return as is
            elif '/' in s3_url and not s3_url.startswith('http'):
                return s3_url
            
            logger.warning(f"Unrecognized S3 URL format: {s3_url}")
            return None
            
        except Exception as e:
            logger.error(f"Error extracting S3 key from URL {s3_url}: {e}")
            return None
    
    def generate_product_image_url(self, product_id: int, filename: str, 
                                 country: str = "colombia") -> str:
        """
        Generate CloudFront URL for a product image
        
        Args:
            product_id: Product ID
            filename: Image filename
            country: Country prefix (default: "colombia")
            
        Returns:
            CloudFront URL for the product image
        """
        s3_key = f"{country}/products/{product_id}/{filename}"
        return self.generate_cloudfront_url(s3_key)
    
    def is_cloudfront_url(self, url: str) -> bool:
        """
        Check if a URL is already a CloudFront URL
        
        Args:
            url: URL to check
            
        Returns:
            True if it's a CloudFront URL
        """
        return url.startswith(self.cloudfront_base_url)
    
    def get_cloudfront_base_url(self) -> str:
        """
        Get the configured CloudFront base URL
        
        Returns:
            CloudFront base URL
        """
        return self.cloudfront_base_url
    
    def validate_configuration(self) -> bool:
        """
        Validate CloudFront configuration
        
        Returns:
            True if configuration is valid
        """
        if not self.cloudfront_base_url:
            logger.error("CloudFront base URL not configured")
            return False
        
        if not self.cloudfront_base_url.startswith('https://'):
            logger.error("CloudFront base URL must use HTTPS")
            return False
        
        if not self.s3_bucket_name:
            logger.warning("S3 bucket name not configured")
        
        logger.info("CloudFront configuration is valid")
        return True

# Global CloudFront URL generator instance
cloudfront_generator = CloudFrontURLGenerator()

# Convenience functions for easy import
def generate_cloudfront_url(s3_key: str) -> str:
    """Generate CloudFront URL from S3 key"""
    return cloudfront_generator.generate_cloudfront_url(s3_key)

def convert_s3_to_cloudfront(s3_url: str) -> str:
    """Convert S3 URL to CloudFront URL"""
    return cloudfront_generator.convert_s3_url_to_cloudfront(s3_url)

def generate_product_image_url(product_id: int, filename: str, country: str = "colombia") -> str:
    """Generate CloudFront URL for product image"""
    return cloudfront_generator.generate_product_image_url(product_id, filename, country)
