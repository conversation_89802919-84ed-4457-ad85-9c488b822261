"""
Data Validation and Error Handling Utilities

This module provides comprehensive validation for migration data and error handling
mechanisms to ensure data integrity during the migration process.
"""

import re
import logging
from typing import List, Dict, Optional, Tuple, Any
from dataclasses import dataclass
from urllib.parse import urlparse
import numpy as np

from ..database.dev_database import ProductWithPhotos, ProductData, ProductPhotoData

logger = logging.getLogger(__name__)

@dataclass
class ValidationResult:
    """Result of data validation"""
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    
    def __post_init__(self):
        if self.errors is None:
            self.errors = []
        if self.warnings is None:
            self.warnings = []

class DataValidator:
    """
    Comprehensive data validator for migration pipeline
    """
    
    def __init__(self):
        # URL validation patterns
        self.s3_url_pattern = re.compile(
            r'^https?://.*\.s3.*\.amazonaws\.com/.*|'
            r'^https?://s3.*\.amazonaws\.com/.*|'
            r'^https?://.*\.s3\..*\.amazonaws\.com/.*'
        )
        
        # Image file extensions
        self.valid_image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp'}
        
        # Minimum/maximum values
        self.min_description_length = 5
        self.max_description_length = 5000
        self.min_product_name_length = 1
        self.max_product_name_length = 500
        self.min_price = 0.01
        self.max_price = 999999999.99
    
    def validate_product_data(self, product: ProductData) -> ValidationResult:
        """
        Validate individual product data
        
        Args:
            product: Product data to validate
            
        Returns:
            ValidationResult with validation details
        """
        errors = []
        warnings = []
        
        # Validate product ID
        if not product.id or product.id <= 0:
            errors.append(f"Invalid product ID: {product.id}")
        
        # Validate product name
        if not product.name or not product.name.strip():
            errors.append("Product name is required")
        elif len(product.name.strip()) < self.min_product_name_length:
            errors.append(f"Product name too short: {len(product.name.strip())} chars")
        elif len(product.name.strip()) > self.max_product_name_length:
            warnings.append(f"Product name very long: {len(product.name.strip())} chars")
        
        # Validate descriptions
        if not product.description and not product.dropi_app_description:
            warnings.append("No description available (will rely on LLM generation)")
        
        if product.description:
            if len(product.description.strip()) < self.min_description_length:
                warnings.append(f"Supplier description very short: {len(product.description.strip())} chars")
            elif len(product.description.strip()) > self.max_description_length:
                warnings.append(f"Supplier description very long: {len(product.description.strip())} chars")
        
        if product.dropi_app_description:
            if len(product.dropi_app_description.strip()) < self.min_description_length:
                warnings.append(f"Dropi description very short: {len(product.dropi_app_description.strip())} chars")
            elif len(product.dropi_app_description.strip()) > self.max_description_length:
                warnings.append(f"Dropi description very long: {len(product.dropi_app_description.strip())} chars")
        
        # Validate price
        if product.sale_price is not None:
            if product.sale_price < self.min_price:
                warnings.append(f"Price seems too low: ${product.sale_price}")
            elif product.sale_price > self.max_price:
                warnings.append(f"Price seems too high: ${product.sale_price}")
        else:
            warnings.append("No price information available")
        
        # Validate SKU
        if not product.sku or not product.sku.strip():
            warnings.append("No SKU available")
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings
        )
    
    def validate_photo_data(self, photo: ProductPhotoData) -> ValidationResult:
        """
        Validate individual photo data
        
        Args:
            photo: Photo data to validate
            
        Returns:
            ValidationResult with validation details
        """
        errors = []
        warnings = []
        
        # Validate photo ID
        if not photo.id or photo.id <= 0:
            errors.append(f"Invalid photo ID: {photo.id}")
        
        # Validate product ID relationship
        if not photo.product_id or photo.product_id <= 0:
            errors.append(f"Invalid product ID in photo: {photo.product_id}")
        
        # Validate S3 URL
        if not photo.url_s3:
            errors.append("S3 URL is required for migration")
        else:
            url_validation = self.validate_s3_url(photo.url_s3)
            if not url_validation.is_valid:
                errors.extend(url_validation.errors)
            warnings.extend(url_validation.warnings)
        
        # Validate regular URL (optional)
        if photo.url:
            if not self.is_valid_url(photo.url):
                warnings.append(f"Regular URL appears invalid: {photo.url}")
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings
        )
    
    def validate_s3_url(self, s3_url: str) -> ValidationResult:
        """
        Validate S3 URL format and accessibility
        
        Args:
            s3_url: S3 URL to validate
            
        Returns:
            ValidationResult with validation details
        """
        errors = []
        warnings = []
        
        if not s3_url or not s3_url.strip():
            errors.append("S3 URL is empty")
            return ValidationResult(is_valid=False, errors=errors, warnings=warnings)
        
        # Check URL format
        if not self.s3_url_pattern.match(s3_url):
            errors.append(f"URL does not appear to be a valid S3 URL: {s3_url}")
        
        # Check if URL looks like an image
        parsed_url = urlparse(s3_url)
        path = parsed_url.path.lower()
        
        # Extract file extension
        if '.' in path:
            extension = '.' + path.split('.')[-1]
            if extension not in self.valid_image_extensions:
                warnings.append(f"URL does not have a recognized image extension: {extension}")
        else:
            warnings.append("URL does not have a file extension")
        
        # Check for common issues
        if 'localhost' in s3_url.lower():
            errors.append("S3 URL contains localhost (invalid)")
        
        if len(s3_url) > 2048:
            warnings.append(f"S3 URL is very long: {len(s3_url)} chars")
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings
        )
    
    def validate_product_with_photos(self, product: ProductWithPhotos) -> ValidationResult:
        """
        Validate complete product with photos data
        
        Args:
            product: Product with photos to validate
            
        Returns:
            ValidationResult with validation details
        """
        all_errors = []
        all_warnings = []
        
        # Validate product data
        product_validation = self.validate_product_data(product.product)
        all_errors.extend(product_validation.errors)
        all_warnings.extend(product_validation.warnings)
        
        # Validate photos
        if not product.photos:
            all_errors.append("Product has no photos")
        else:
            s3_photos = [photo for photo in product.photos if photo.url_s3]
            if not s3_photos:
                all_errors.append("Product has no S3 photos available for migration")
            
            for i, photo in enumerate(product.photos):
                photo_validation = self.validate_photo_data(photo)
                
                # Add photo index to error messages for clarity
                for error in photo_validation.errors:
                    all_errors.append(f"Photo {i+1}: {error}")
                for warning in photo_validation.warnings:
                    all_warnings.append(f"Photo {i+1}: {warning}")
        
        return ValidationResult(
            is_valid=len(all_errors) == 0,
            errors=all_errors,
            warnings=all_warnings
        )
    
    def validate_embedding(self, embedding: np.ndarray) -> ValidationResult:
        """
        Validate embedding vector
        
        Args:
            embedding: Numpy array representing the embedding
            
        Returns:
            ValidationResult with validation details
        """
        errors = []
        warnings = []
        
        if embedding is None:
            errors.append("Embedding is None")
            return ValidationResult(is_valid=False, errors=errors, warnings=warnings)
        
        if not isinstance(embedding, np.ndarray):
            errors.append(f"Embedding is not a numpy array: {type(embedding)}")
            return ValidationResult(is_valid=False, errors=errors, warnings=warnings)
        
        # Check dimensions
        if embedding.shape != (1024,):
            errors.append(f"Embedding has wrong dimensions: {embedding.shape}, expected (1024,)")
        
        # Check for NaN or infinite values
        if np.isnan(embedding).any():
            errors.append("Embedding contains NaN values")
        
        if np.isinf(embedding).any():
            errors.append("Embedding contains infinite values")
        
        # Check if embedding is normalized (for cosine similarity)
        norm = np.linalg.norm(embedding)
        if abs(norm - 1.0) > 0.1:
            warnings.append(f"Embedding may not be normalized (norm: {norm:.3f})")
        
        # Check if embedding is all zeros
        if np.allclose(embedding, 0):
            warnings.append("Embedding is all zeros")
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings
        )
    
    def validate_batch(self, products: List[ProductWithPhotos]) -> Dict[str, Any]:
        """
        Validate a batch of products and return comprehensive results
        
        Args:
            products: List of products to validate
            
        Returns:
            Dictionary with validation summary and details
        """
        if not products:
            return {
                "is_valid": False,
                "total_products": 0,
                "valid_products": 0,
                "invalid_products": 0,
                "total_errors": 1,
                "total_warnings": 0,
                "errors": ["No products provided for validation"],
                "warnings": [],
                "product_results": []
            }
        
        valid_count = 0
        invalid_count = 0
        all_errors = []
        all_warnings = []
        product_results = []
        
        for i, product in enumerate(products):
            validation = self.validate_product_with_photos(product)
            
            product_result = {
                "product_id": product.product.id,
                "product_name": product.product.name,
                "is_valid": validation.is_valid,
                "errors": validation.errors,
                "warnings": validation.warnings,
                "photos_count": len(product.photos),
                "s3_photos_count": len([p for p in product.photos if p.url_s3])
            }
            
            product_results.append(product_result)
            
            if validation.is_valid:
                valid_count += 1
            else:
                invalid_count += 1
            
            # Add product context to errors and warnings
            for error in validation.errors:
                all_errors.append(f"Product {product.product.id} ({product.product.name}): {error}")
            for warning in validation.warnings:
                all_warnings.append(f"Product {product.product.id} ({product.product.name}): {warning}")
        
        return {
            "is_valid": invalid_count == 0,
            "total_products": len(products),
            "valid_products": valid_count,
            "invalid_products": invalid_count,
            "total_errors": len(all_errors),
            "total_warnings": len(all_warnings),
            "errors": all_errors,
            "warnings": all_warnings,
            "product_results": product_results
        }
    
    def is_valid_url(self, url: str) -> bool:
        """
        Check if a URL is valid
        
        Args:
            url: URL to validate
            
        Returns:
            bool: True if URL is valid
        """
        try:
            result = urlparse(url)
            return all([result.scheme, result.netloc])
        except Exception:
            return False

# Global validator instance
data_validator = DataValidator()
