from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime

class CompanyCreateRequest(BaseModel):
    """Request model for creating a company"""
    pass

class CompanyCreateResponse(BaseModel):
    """Response model for company creation"""
    success: bool
    message: str
    company_name: str

class ImageInsertRequest(BaseModel):
    """Request model for inserting an image"""
    idProducto: int
    idPhoto: int
    urlPhoto: Optional[str] = None
    texto: Optional[str] = None

class ImageInsertResponse(BaseModel):
    """Response model for image insertion"""
    success: bool
    message: str
    company_name: str
    idProducto: int
    idPhoto: int
    texto_generado: Optional[str] = None
    embedding_dimension: int

class SearchRequest(BaseModel):
    """Request model for semantic search"""
    company_name: str
    query_text: str
    limit: Optional[int] = 10

class MultiVendorSearchRequest(BaseModel):
    """Request model for multi-vendor semantic search"""
    query_text: str
    limit: Optional[int] = 10

class Product(BaseModel):
    """Individual product model"""
    empresa: str
    idProducto: int
    idPhoto: int
    urlPhoto: Optional[str]
    texto: Optional[str]

class ProductsResponse(BaseModel):
    """Response model for products list"""
    success: bool
    company_name: str
    products: List[Product]
    total_products: int

class SearchResult(BaseModel):
    """Individual search result"""
    empresa: str
    idProducto: int
    idPhoto: int
    urlPhoto: Optional[str]
    texto: Optional[str]
    distancia_coseno: float

class SearchResponse(BaseModel):
    """Response model for semantic search"""
    success: bool
    query_text: str
    company_name: str
    results: List[SearchResult]
    total_results: int

class MultiVendorSearchResponse(BaseModel):
    """Response model for multi-vendor semantic search"""
    success: bool
    query_text: str
    results: List[SearchResult]
    total_results: int
    vendors_found: List[str]

class ReindexRequest(BaseModel):
    """Request model for reindexing"""
    pass

class ReindexResponse(BaseModel):
    """Response model for reindexing"""
    success: bool
    message: str
    company_name: str

class HealthCheckResponse(BaseModel):
    """Health check response"""
    status: str
    timestamp: str
    database_connected: bool
    openai_configured: bool
    version: str