from fastapi import APIRouter, HTTPException, status
from typing import Optional
from ..models.schemas import (
    CompanyCreateRequest, CompanyCreateResponse,
    ImageInsertRequest, ImageInsertResponse,
    SearchRequest, SearchResponse, SearchResult,
    MultiVendorSearchRequest, MultiVendorSearchResponse,
    ReindexRequest, ReindexResponse,
    ProductsResponse, Product
)
from ..database import db_manager
from ..services.embedding_service import embedding_service
from ..services.llm_service import llm_service

router = APIRouter(prefix="/api/v1", tags=["semantic-search"])

@router.post("/companies/{company_name}/create", response_model=CompanyCreateResponse)
async def create_company(company_name: str, request: CompanyCreateRequest):
    """
    Create a new company/country table with vector index and update the unified view
    """
    try:
        # Validate company name
        if not company_name or not company_name.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Company name cannot be empty"
            )
        
        company_name = company_name.strip().upper()
        
        # Create company table
        success = db_manager.create_company_table(company_name)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to create table for company {company_name}"
            )
        
        return CompanyCreateResponse(
            success=True,
            message=f"Company table {company_name} created successfully with vector index",
            company_name=company_name
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )

@router.post("/companies/{company_name}/reindex", response_model=ReindexResponse)
async def reindex_company(company_name: str, request: ReindexRequest):
    """
    Recreate the vector index for a specific company
    """
    try:
        company_name = company_name.strip().upper()
        
        # Check if company table exists
        existing_companies = db_manager.get_existing_companies()
        if company_name not in [c.upper() for c in existing_companies]:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Company table {company_name} not found"
            )
        
        # Recreate vector index
        success = db_manager.create_vector_index(company_name)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to recreate index for company {company_name}"
            )
        
        return ReindexResponse(
            success=True,
            message=f"Vector index for company {company_name} recreated successfully",
            company_name=company_name
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )

@router.get("/companies/{company_name}/products", response_model=ProductsResponse)
async def get_company_products(company_name: str):
    """
    Get all products for a company
    """
    try:
        company_name = company_name.strip().upper()

        # Check if company table exists
        existing_companies = db_manager.get_existing_companies()
        if company_name not in [c.upper() for c in existing_companies]:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Company table {company_name} not found. Create it first."
            )

        # Get all products
        products_data = db_manager.get_all_products(company_name)

        # Convert to Product models
        products = [Product(**product) for product in products_data]

        return ProductsResponse(
            success=True,
            company_name=company_name,
            products=products,
            total_products=len(products)
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )

@router.get("/companies/{company_name}/next-product-id")
async def get_next_product_id(company_name: str):
    """
    Get the next available product ID for a company
    """
    try:
        company_name = company_name.strip().upper()

        # Check if company table exists
        existing_companies = db_manager.get_existing_companies()
        if company_name not in [c.upper() for c in existing_companies]:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Company table {company_name} not found. Create it first."
            )

        # Get next product ID
        next_id = db_manager.get_next_product_id(company_name)

        return {
            "success": True,
            "company_name": company_name,
            "next_product_id": next_id,
            "message": f"Next available product ID for {company_name} is {next_id}"
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )

@router.post("/companies/{company_name}/images", response_model=ImageInsertResponse)
async def insert_image(company_name: str, request: ImageInsertRequest):
    """
    Insert a new image for a company. Generates description and embedding.
    """
    try:
        company_name = company_name.strip().upper()
        
        # Check if company table exists
        existing_companies = db_manager.get_existing_companies()
        if company_name not in [c.upper() for c in existing_companies]:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Company table {company_name} not found. Create it first."
            )
        
        # Generate description if not provided
        texto_final = request.texto
        if not texto_final and request.urlPhoto:
            # Generate description using LLM
            texto_final = llm_service.generate_image_description(
                image_url=request.urlPhoto,
                existing_text=request.texto
            )
        elif not texto_final:
            # No text and no URL provided
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Either texto or urlPhoto must be provided"
            )
        
        # Generate embedding
        embedding = embedding_service.get_embedding(texto_final)
        if embedding is None:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to generate embedding"
            )
        
        # Insert into database
        success = db_manager.insert_image(
            company_name=company_name,
            id_producto=request.idProducto,
            id_photo=request.idPhoto,
            url_photo=request.urlPhoto,
            texto=texto_final,
            embedding=embedding
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to insert image into database"
            )
        
        return ImageInsertResponse(
            success=True,
            message="Image inserted successfully",
            company_name=company_name,
            idProducto=request.idProducto,
            idPhoto=request.idPhoto,
            texto_generado=texto_final,
            embedding_dimension=len(embedding)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )

@router.post("/search", response_model=SearchResponse)
async def search_images(request: SearchRequest):
    """
    Search for similar images using semantic search
    """
    try:
        company_name = request.company_name.strip().upper()
        
        # Check if company table exists
        existing_companies = db_manager.get_existing_companies()
        if company_name not in [c.upper() for c in existing_companies]:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Company table {company_name} not found"
            )
        
        # Clean query text and extract price info
        clean_query, price_info = llm_service.clean_search_query(request.query_text)
        
        # Generate embedding for the query
        query_embedding = embedding_service.get_embedding(clean_query)
        if query_embedding is None:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to generate embedding for query"
            )
        
        # Search for similar images
        results = db_manager.search_similar_images(
            company_name=company_name,
            query_embedding=query_embedding,
            limit=request.limit
        )
        
        # Convert to response format
        search_results = [
            SearchResult(
                empresa=result["empresa"],
                idProducto=result["idProducto"],
                idPhoto=result["idPhoto"],
                urlPhoto=result["urlPhoto"],
                texto=result["texto"],
                distancia_coseno=result["distancia_coseno"]
            )
            for result in results
        ]
        
        return SearchResponse(
            success=True,
            query_text=request.query_text,
            company_name=company_name,
            results=search_results,
            total_results=len(search_results)
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )

@router.post("/search/all-vendors", response_model=MultiVendorSearchResponse)
async def search_images_all_vendors(request: MultiVendorSearchRequest):
    """
    Search for similar images across ALL vendors using semantic search
    """
    try:
        # Clean query text and extract price info
        clean_query, price_info = llm_service.clean_search_query(request.query_text)

        # Generate embedding for the query
        query_embedding = embedding_service.get_embedding(clean_query)
        if query_embedding is None:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to generate embedding for query"
            )

        # Search for similar images across all vendors
        results = db_manager.search_similar_images_all_vendors(
            query_embedding=query_embedding,
            limit=request.limit
        )

        # Convert to response format
        search_results = [
            SearchResult(
                empresa=result["empresa"],
                idProducto=result["idProducto"],
                idPhoto=result["idPhoto"],
                urlPhoto=result["urlPhoto"],
                texto=result["texto"],
                distancia_coseno=result["distancia_coseno"]
            )
            for result in results
        ]

        # Get unique vendors found
        vendors_found = list(set([result["empresa"] for result in results]))

        return MultiVendorSearchResponse(
            success=True,
            query_text=request.query_text,
            results=search_results,
            total_results=len(search_results),
            vendors_found=vendors_found
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )