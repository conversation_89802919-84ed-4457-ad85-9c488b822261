"""
Migration API Router

FastAPI endpoints for managing data migration from development database
to semantic search database.
"""

from fastapi import APIRouter, HTTPException, status, BackgroundTasks
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime
import asyncio
import logging
import json
import os

from ..database.dev_database import dev_db_manager
from ..services.migration_service import migration_service, MigrationResult, MigrationStats
from ..models.schemas import HealthCheckResponse

# Configure logging
logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/migration", tags=["migration"])

# Global storage for migration status (in production, use Redis or database)
migration_status_store = {}

class MigrationRequest(BaseModel):
    """Request model for starting migration"""
    company_name: str = Field(default="dropi_colombia", description="Company name for the migration")
    batch_size: int = Field(default=50, ge=1, le=500, description="Number of products to process in each batch")
    sample_size: Optional[int] = Field(default=None, ge=1, le=100, description="Limit migration to sample size for testing")
    force_recreate_table: bool = Field(default=False, description="Force recreation of company table")

class MigrationStatusResponse(BaseModel):
    """Response model for migration status"""
    migration_id: str
    status: str  # "running", "completed", "failed", "not_found"
    company_name: str
    progress: Dict[str, Any]
    start_time: Optional[datetime]
    end_time: Optional[datetime]
    error_message: Optional[str] = None

class MigrationStartResponse(BaseModel):
    """Response model for starting migration"""
    success: bool
    migration_id: str
    message: str
    estimated_products: int

class DatabaseAnalysisResponse(BaseModel):
    """Response model for database analysis"""
    success: bool
    analysis: Dict[str, Any]

class MigrationReportResponse(BaseModel):
    """Response model for migration report"""
    success: bool
    migration_id: str
    summary: Dict[str, Any]
    detailed_results: List[Dict[str, Any]]

@router.get("/health", response_model=HealthCheckResponse)
async def migration_health_check():
    """
    Health check for migration services
    """
    try:
        # Test development database connection
        dev_db_connected = dev_db_manager.test_connection()
        
        # Test migration service components
        migration_service_ready = True
        try:
            # Test embedding service
            test_embedding = migration_service.embedding_service.get_embedding("test")
            embedding_ready = test_embedding is not None
            
            # Test LLM service
            llm_ready = migration_service.llm_service.test_connection()
            
            migration_service_ready = embedding_ready and llm_ready
        except Exception as e:
            logger.error(f"Migration service health check failed: {e}")
            migration_service_ready = False
        
        return HealthCheckResponse(
            status="healthy" if dev_db_connected and migration_service_ready else "unhealthy",
            timestamp=datetime.now().isoformat(),
            database_connected=dev_db_connected,
            openai_configured=migration_service_ready,  # Reusing this field for migration services
            version="1.0.0"
        )
    except Exception as e:
        logger.error(f"Migration health check error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Health check failed: {str(e)}"
        )

@router.get("/analyze-database", response_model=DatabaseAnalysisResponse)
async def analyze_development_database():
    """
    Analyze the development database to understand migration scope
    """
    try:
        # Import here to avoid circular imports
        from ..database.dev_database import dev_db_manager
        
        # Test connection first
        if not dev_db_manager.test_connection():
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Cannot connect to development database"
            )
        
        # Get database statistics
        stats = dev_db_manager.get_database_stats()
        
        # Additional analysis
        analysis = {
            "database_stats": stats,
            "migration_readiness": {},
            "recommendations": []
        }
        
        total_products = stats.get('total_products', 0)
        products_with_s3 = stats.get('products_with_s3_photos', 0)
        products_with_desc = stats.get('products_with_descriptions', 0)
        
        if total_products > 0:
            s3_coverage = (products_with_s3 / total_products) * 100
            desc_coverage = (products_with_desc / total_products) * 100
            
            analysis["migration_readiness"] = {
                "s3_photo_coverage_percent": round(s3_coverage, 2),
                "description_coverage_percent": round(desc_coverage, 2),
                "products_ready_for_migration": products_with_s3,
                "estimated_migration_time_minutes": round(products_with_s3 * 0.5, 1)  # Rough estimate
            }
            
            # Add recommendations
            if s3_coverage < 50:
                analysis["recommendations"].append(
                    f"Low S3 photo coverage ({s3_coverage:.1f}%). Consider focusing on products with S3 images."
                )
            
            if desc_coverage < 30:
                analysis["recommendations"].append(
                    f"Low description coverage ({desc_coverage:.1f}%). LLM enhancement will be crucial."
                )
            
            if products_with_s3 > 1000:
                analysis["recommendations"].append(
                    "Large dataset detected. Consider batch processing with progress tracking."
                )
            
            if products_with_s3 < 10:
                analysis["recommendations"].append(
                    "Small dataset detected. Perfect for testing migration pipeline."
                )
        
        return DatabaseAnalysisResponse(
            success=True,
            analysis=analysis
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Database analysis error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Database analysis failed: {str(e)}"
        )

async def run_migration_background(migration_id: str, request: MigrationRequest):
    """
    Background task to run the migration process
    """
    try:
        logger.info(f"Starting background migration {migration_id}")
        
        # Update status to running
        migration_status_store[migration_id] = {
            "status": "running",
            "company_name": request.company_name,
            "start_time": datetime.now(),
            "progress": {"phase": "initializing", "processed": 0, "total": 0},
            "error_message": None
        }
        
        # Initialize migration service for this company
        migration_svc = migration_service
        migration_svc.company_name = request.company_name
        
        # Ensure company table exists
        if not migration_svc.ensure_company_table_exists():
            raise Exception("Failed to create/verify company table")
        
        # Extract products from development database
        migration_status_store[migration_id]["progress"]["phase"] = "extracting_data"
        
        if request.sample_size:
            # Extract sample data
            from ..database.dev_database import dev_db_manager
            products = dev_db_manager.extract_products_batch(offset=0, limit=request.sample_size)
            # Filter to only products with S3 photos
            products = [p for p in products if any(photo.url_s3 for photo in p.photos)]
        else:
            # Extract all data in batches
            from ..database.dev_database import dev_db_manager
            all_products = []
            offset = 0
            
            while True:
                batch = dev_db_manager.extract_products_batch(offset=offset, limit=request.batch_size)
                if not batch:
                    break
                
                # Filter to only products with S3 photos
                s3_products = [p for p in batch if any(photo.url_s3 for photo in p.photos)]
                all_products.extend(s3_products)
                
                offset += request.batch_size
                
                # Update progress
                migration_status_store[migration_id]["progress"]["extracted"] = len(all_products)
            
            products = all_products
        
        if not products:
            raise Exception("No products with S3 photos found for migration")
        
        # Update progress
        migration_status_store[migration_id]["progress"]["total"] = len(products)
        migration_status_store[migration_id]["progress"]["phase"] = "processing_products"
        
        # Process products in batches
        all_results = []
        all_stats = MigrationStats(total_products=len(products), start_time=datetime.now())
        
        for i in range(0, len(products), request.batch_size):
            batch = products[i:i + request.batch_size]
            
            # Process batch
            batch_results, batch_stats = await migration_svc.migrate_products_batch(batch)
            all_results.extend(batch_results)
            
            # Update cumulative stats
            all_stats.successful_products += batch_stats.successful_products
            all_stats.failed_products += batch_stats.failed_products
            all_stats.total_photos_processed += batch_stats.total_photos_processed
            all_stats.total_processing_time += batch_stats.total_processing_time
            all_stats.errors.extend(batch_stats.errors)
            
            # Update progress
            migration_status_store[migration_id]["progress"]["processed"] = len(all_results)
            migration_status_store[migration_id]["progress"]["successful"] = all_stats.successful_products
            migration_status_store[migration_id]["progress"]["failed"] = all_stats.failed_products
        
        all_stats.end_time = datetime.now()
        
        # Save migration report
        report_filename = f"output/migration_reports/migration_{migration_id}.json"
        migration_svc.save_migration_report(all_results, all_stats, report_filename)
        
        # Update final status
        migration_status_store[migration_id].update({
            "status": "completed",
            "end_time": datetime.now(),
            "progress": {
                "phase": "completed",
                "processed": len(all_results),
                "total": len(products),
                "successful": all_stats.successful_products,
                "failed": all_stats.failed_products,
                "photos_processed": all_stats.total_photos_processed
            },
            "report_file": report_filename
        })
        
        logger.info(f"Migration {migration_id} completed successfully")
        
    except Exception as e:
        logger.error(f"Migration {migration_id} failed: {e}")
        migration_status_store[migration_id].update({
            "status": "failed",
            "end_time": datetime.now(),
            "error_message": str(e)
        })

@router.post("/start", response_model=MigrationStartResponse)
async def start_migration(request: MigrationRequest, background_tasks: BackgroundTasks):
    """
    Start a new migration process
    """
    try:
        # Generate unique migration ID
        migration_id = f"migration_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{request.company_name}"
        
        # Validate development database connection
        if not dev_db_manager.test_connection():
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Cannot connect to development database"
            )
        
        # Get estimated product count
        if request.sample_size:
            estimated_products = min(request.sample_size, dev_db_manager.get_products_with_photos_count())
        else:
            estimated_products = dev_db_manager.get_products_with_photos_count()
        
        if estimated_products == 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No products with S3 photos found for migration"
            )
        
        # Start background migration
        background_tasks.add_task(run_migration_background, migration_id, request)
        
        return MigrationStartResponse(
            success=True,
            migration_id=migration_id,
            message=f"Migration started for {request.company_name}",
            estimated_products=estimated_products
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to start migration: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start migration: {str(e)}"
        )

@router.get("/status/{migration_id}", response_model=MigrationStatusResponse)
async def get_migration_status(migration_id: str):
    """
    Get the status of a migration process
    """
    if migration_id not in migration_status_store:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Migration {migration_id} not found"
        )
    
    status_data = migration_status_store[migration_id]
    
    return MigrationStatusResponse(
        migration_id=migration_id,
        status=status_data["status"],
        company_name=status_data["company_name"],
        progress=status_data["progress"],
        start_time=status_data["start_time"],
        end_time=status_data.get("end_time"),
        error_message=status_data.get("error_message")
    )

@router.get("/report/{migration_id}", response_model=MigrationReportResponse)
async def get_migration_report(migration_id: str):
    """
    Get detailed migration report
    """
    if migration_id not in migration_status_store:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Migration {migration_id} not found"
        )
    
    status_data = migration_status_store[migration_id]
    
    if status_data["status"] != "completed":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Migration {migration_id} is not completed yet"
        )
    
    # Load report file if available
    report_file = status_data.get("report_file")
    if report_file and os.path.exists(report_file):
        with open(report_file, 'r', encoding='utf-8') as f:
            report_data = json.load(f)
        
        return MigrationReportResponse(
            success=True,
            migration_id=migration_id,
            summary=report_data["migration_summary"],
            detailed_results=report_data["detailed_results"]
        )
    else:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Report file not found for migration {migration_id}"
        )
