import os
from datetime import datetime
from fastapi import FastAP<PERSON>, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from dotenv import load_dotenv

from .routers import semantic_search, file_upload, migration
from .models.schemas import HealthCheckResponse
from .database import db_manager
from .services.embedding_service import embedding_service
from .services.llm_service import llm_service

# Load environment variables
load_dotenv()

# Create FastAPI app
app = FastAPI(
    title="Semantic Search POC",
    description="Proof of Concept for semantic search using PostgreSQL with pgvector",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # For POC - in production, specify allowed origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")
app.mount("/uploads", StaticFiles(directory="uploads"), name="uploads")

# Include routers
app.include_router(semantic_search.router)
app.include_router(file_upload.router)
app.include_router(migration.router)

@app.get("/health", response_model=HealthCheckResponse)
async def health_check():
    """
    Health check endpoint
    """
    # Test database connection
    db_connected = db_manager.test_connection()
    
    # Test AWS Bedrock connection
    aws_configured = bool(os.getenv("AWS_PROFILE")) and bool(os.getenv("AWS_REGION"))
    aws_connected = False
    if aws_configured:
        aws_connected = embedding_service.test_connection()

    return HealthCheckResponse(
        status="ok" if db_connected and aws_connected else "degraded",
        timestamp=datetime.now().isoformat(),
        database_connected=db_connected,
        openai_configured=aws_connected,  # Keeping same field name for compatibility
        version="1.0.0"
    )

@app.get("/")
async def root():
    """
    Serve the consolidated main interface with automatic description generation
    """
    return FileResponse("static/index.html")

@app.get("/api")
async def api_info():
    """
    API information endpoint
    """
    return {
        "message": "Semantic Search POC API",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/health"
    }

if __name__ == "__main__":
    import uvicorn
    
    host = os.getenv("APP_HOST", "0.0.0.0")
    port = int(os.getenv("APP_PORT", 8000))
    debug = os.getenv("DEBUG", "True").lower() == "true"
    
    uvicorn.run(
        "app.main:app",
        host=host,
        port=port,
        reload=debug
    )