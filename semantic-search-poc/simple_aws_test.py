#!/usr/bin/env python3
"""
Simple AWS Bedrock test without external dependencies
"""

import os
import json
import boto3
from botocore.config import Config

def test_aws_bedrock():
    """Test AWS Bedrock connection and basic functionality"""
    print("🚀 Testing AWS Bedrock Connection")
    print("=" * 40)
    
    # AWS configuration
    aws_region = "us-east-2"
    aws_profile = "IA"
    
    print(f"AWS Region: {aws_region}")
    print(f"AWS Profile: {aws_profile}")
    
    try:
        # Configure boto3 session with profile
        session = boto3.Session(profile_name=aws_profile)
        
        # Configure retry settings
        retry_config = Config(
            region_name=aws_region,
            retries={
                "max_attempts": 3,
                "mode": "standard",
            },
        )
        
        # Initialize Bedrock runtime client
        client = session.client(
            service_name='bedrock-runtime',
            region_name=aws_region,
            config=retry_config
        )
        
        print("\n✅ Bedrock client initialized successfully")
        
        # Test Titan Embeddings
        print("\n🔍 Testing Titan Text Embeddings V2...")
        
        embedding_body = json.dumps({
            "inputText": "test embedding",
            "dimensions": 1024,
            "normalize": True
        })
        
        embedding_response = client.invoke_model(
            body=embedding_body,
            modelId="amazon.titan-embed-text-v2:0",
            accept="application/json",
            contentType="application/json"
        )
        
        embedding_result = json.loads(embedding_response.get('body').read())
        embedding = embedding_result['embedding']
        
        print(f"✅ Titan Embeddings: Generated {len(embedding)} dimensions")
        
        # Test Nova Micro
        print("\n🤖 Testing Nova Micro...")
        
        nova_messages = [
            {
                "role": "user",
                "content": [
                    {
                        "text": "Describe un zapato negro en español en una frase."
                    }
                ]
            }
        ]
        
        nova_response = client.converse(
            modelId="us.amazon.nova-micro-v1:0",
            messages=nova_messages,
            inferenceConfig={
                "maxTokens": 50,
                "temperature": 0.3
            }
        )
        
        nova_text = nova_response['output']['message']['content'][0]['text']
        print(f"✅ Nova Micro: {nova_text}")
        
        print("\n🎉 All AWS Bedrock tests passed!")
        return True
        
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_aws_bedrock()
    exit(0 if success else 1)