/* Estilos básicos para la aplicación */
body {
    background-color: #f8f9fa;
}

.card {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: none;
}

.card-header {
    background-color: #f1f8ff;
    border-bottom: 1px solid #e3f2fd;
}

.card-img-top {
    height: 200px;
    object-fit: contain;
    padding: 10px;
    background-color: #f8f9fa;
}

/* Estilos para los resultados de búsqueda */
#searchResults .card {
    transition: transform 0.2s;
}

#searchResults .card:hover {
    transform: translateY(-5px);
}

/* Estilos para el formulario */
.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Estilos para los botones */
.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
}

.btn-primary:hover {
    background-color: #0069d9;
    border-color: #0062cc;
}

/* Estilos para la información de la consulta */
#originalQuery, #cleanQuery {
    padding: 8px 12px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border-left: 3px solid #007bff;
}

/* Estilos para la vista previa de descripción */
#descriptionPreview {
    animation: fadeIn 0.3s ease-in;
}

#descriptionPreview .alert {
    border-left: 4px solid #17a2b8;
}

#generatedDescription {
    font-style: italic;
    color: #495057;
    background-color: #f8f9fa;
    padding: 10px;
    border-radius: 4px;
    border: 1px solid #dee2e6;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Estilos para indicadores de carga */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Estilos mejorados para las tarjetas de productos */
.card-body h5 {
    color: #495057;
    font-weight: 600;
}

.card-text strong {
    color: #6c757d;
}