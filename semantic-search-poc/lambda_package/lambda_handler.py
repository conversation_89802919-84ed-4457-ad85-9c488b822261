"""
Lambda handler for the semantic search application
Adapts the FastAPI application to work with AWS Lambda
"""
import os
import json
import base64
from typing import Dict, Any
from mangum import Mangum
from app.main import app

# Set environment variables for Lambda execution
os.environ.setdefault("AWS_REGION", "us-east-2")
os.environ.setdefault("AWS_PROFILE", "IA")

# Create the Lambda handler using Mangum
lambda_handler = Mangum(app, lifespan="off")

def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    AWS Lambda handler function
    
    Args:
        event: Lambda event data
        context: Lambda context object
        
    Returns:
        Lambda response
    """
    try:
        # Handle different event types
        if "requestContext" in event:
            # This is a Lambda Function URL request
            return lambda_handler(event, context)
        else:
            # Handle other event types if needed
            return {
                "statusCode": 400,
                "headers": {
                    "Content-Type": "application/json",
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
                    "Access-Control-Allow-Headers": "Content-Type, Authorization"
                },
                "body": json.dumps({
                    "error": "Unsupported event type",
                    "event_type": type(event).__name__
                })
            }
    except Exception as e:
        print(f"Lambda handler error: {str(e)}")
        return {
            "statusCode": 500,
            "headers": {
                "Content-Type": "application/json",
                "Access-Control-Allow-Origin": "*"
            },
            "body": json.dumps({
                "error": "Internal server error",
                "message": str(e)
            })
        }
