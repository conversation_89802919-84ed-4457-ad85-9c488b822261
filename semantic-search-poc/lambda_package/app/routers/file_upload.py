from fastapi import APIRouter, UploadFile, File, HTTPException, Form
from fastapi.responses import JSONResponse
from typing import Optional
import os
import base64
from ..services.storage_service import storage_service
from ..services.llm_service import llm_service

router = APIRouter(prefix="/api/v1/files", tags=["files"])

@router.post("/generate-description")
async def generate_description(
    file: UploadFile = File(...),
    company: str = Form(...)
):
    """
    Generate automatic description from uploaded image using Nova Micro (cheapest model)

    Args:
        file: Image file to analyze
        company: Company name

    Returns:
        JSON response with generated description and image data
    """
    try:
        # Validate file type
        if not file.content_type or not file.content_type.startswith('image/'):
            raise HTTPException(
                status_code=400,
                detail="Solo se permiten archivos de imagen (JPG, PNG, WebP, etc.)"
            )

        # Validate file size (max 10MB)
        max_size = 10 * 1024 * 1024  # 10MB
        file_content = await file.read()
        if len(file_content) > max_size:
            raise HTTPException(
                status_code=400,
                detail="Archivo demasiado grande. Tamaño máximo es 10MB."
            )

        # Generate description using Nova Micro
        description = llm_service.generate_image_description(image_bytes=file_content)

        if not description or description.startswith("Error"):
            raise HTTPException(
                status_code=500,
                detail="No se pudo analizar la imagen. Intenta con otra imagen"
            )

        # Convert image to base64 for frontend preview
        encoded_image = base64.b64encode(file_content).decode("utf-8")

        return JSONResponse(
            status_code=200,
            content={
                "success": True,
                "descripcion": description,
                "imagen_base64": encoded_image,
                "company": company,
                "filename": file.filename
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error interno del servidor: {str(e)}"
        )

@router.post("/upload")
async def upload_file(
    file: UploadFile = File(...),
    company: str = Form(...),
    description: str = Form(...),  # Now required - comes from auto-generation
    product_id: Optional[int] = Form(None),  # Made optional - can be auto-generated
    photo_id: int = Form(1)
):
    """
    Upload an image file with auto-generated description

    Args:
        file: Image file to upload
        company: Company name
        description: Auto-generated product description (required)
        product_id: Product ID (optional - can be auto-generated)
        photo_id: Photo ID (default: 1)

    Returns:
        JSON response with file URL and upload status
    """
    try:
        # Validate file type
        if not file.content_type or not file.content_type.startswith('image/'):
            raise HTTPException(
                status_code=400,
                detail="Only image files are allowed (JPG, PNG, WebP, etc.)"
            )
        
        # Validate file size (max 10MB)
        max_size = 10 * 1024 * 1024  # 10MB
        file_content = await file.read()
        if len(file_content) > max_size:
            raise HTTPException(
                status_code=400,
                detail="File size too large. Maximum size is 10MB."
            )
        
        # Reset file pointer for storage service
        await file.seek(0)
        
        # Upload file using storage service
        file_url = await storage_service.upload_file(
            file_content=file.file,
            filename=file.filename,
            content_type=file.content_type
        )
        
        return JSONResponse(
            status_code=200,
            content={
                "success": True,
                "message": "File uploaded successfully",
                "file_url": file_url,
                "filename": file.filename,
                "content_type": file.content_type,
                "size": len(file_content),
                "company": company,
                "product_id": product_id,
                "photo_id": photo_id,
                "description": description
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to upload file: {str(e)}"
        )

@router.delete("/delete")
async def delete_file(file_url: str):
    """
    Delete an uploaded file
    
    Args:
        file_url: URL of the file to delete
        
    Returns:
        JSON response with deletion status
    """
    try:
        success = await storage_service.delete_file(file_url)
        
        if success:
            return JSONResponse(
                status_code=200,
                content={
                    "success": True,
                    "message": "File deleted successfully",
                    "file_url": file_url
                }
            )
        else:
            raise HTTPException(
                status_code=404,
                detail="File not found or could not be deleted"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to delete file: {str(e)}"
        )
