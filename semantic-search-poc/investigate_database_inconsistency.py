#!/usr/bin/env python3
"""
Database Inconsistency Investigation Script
Comprehensive analysis of company tables and data distribution
"""
import requests
import json
import subprocess
import os
from typing import Dict, List, Any

BASE_URL = "http://localhost:8000"

def check_api_health():
    """Check if API is running and healthy"""
    print("🔍 Checking API Health...")
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API Status: {data['status']}")
            print(f"✅ Database Connected: {data['database_connected']}")
            return True
        else:
            print(f"❌ API health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Cannot connect to API: {e}")
        return False

def investigate_database_structure():
    """Investigate database structure using direct SQL queries"""
    print("\n🗄️ Investigating Database Structure...")
    
    db_url = "postgresql://postgres:postgres@localhost:5432/semantic_search"
    
    try:
        # Check all tables that match the pattern
        print("   📋 Finding all company tables...")
        result = subprocess.run([
            "docker", "exec", "semantic_search_db", "psql", "-U", "postgres", "-d", "semantic_search", "-c",
            "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name LIKE 'imagenes_%' ORDER BY table_name;"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("   ✅ Company tables found:")
            tables = []
            lines = result.stdout.split('\n')
            for line in lines:
                line = line.strip()
                if line and 'imagenes_' in line.lower() and not line.startswith('-') and '|' not in line and 'table_name' not in line:
                    tables.append(line)
                    print(f"      - {line}")
            
            # Count records in each table
            print("\n   📊 Record counts per table:")
            for table in tables:
                count_result = subprocess.run([
                    "docker", "exec", "semantic_search_db", "psql", "-U", "postgres", "-d", "semantic_search", "-c",
                    f"SELECT COUNT(*) FROM {table};"
                ], capture_output=True, text=True, timeout=10)
                
                if count_result.returncode == 0:
                    count_lines = count_result.stdout.split('\n')
                    for line in count_lines:
                        line = line.strip()
                        if line.isdigit():
                            print(f"      {table}: {line} records")
                            break
            
            return tables
        else:
            print(f"   ❌ Error querying tables: {result.stderr}")
            return []
            
    except Exception as e:
        print(f"   ❌ Error investigating database: {e}")
        return []

def check_specific_table_content(table_name: str):
    """Check content of a specific table"""
    print(f"\n🔍 Investigating {table_name} content...")
    
    try:
        # Get sample records
        result = subprocess.run([
            "docker", "exec", "semantic_search_db", "psql", "-U", "postgres", "-d", "semantic_search", "-c",
            f"SELECT empresa, idproducto, idphoto, LEFT(texto, 50) as texto_preview, array_length(embedding, 1) as embedding_dim FROM {table_name} ORDER BY idproducto, idphoto;"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print(f"   📋 Records in {table_name}:")
            lines = result.stdout.split('\n')
            for line in lines:
                if '|' in line and not line.startswith('-') and 'empresa' not in line:
                    print(f"      {line.strip()}")
        else:
            print(f"   ❌ Error querying {table_name}: {result.stderr}")
            
    except Exception as e:
        print(f"   ❌ Error checking {table_name}: {e}")

def check_imagenes_view():
    """Check the unified IMAGENES view"""
    print(f"\n👁️ Investigating IMAGENES view...")
    
    try:
        # Check if view exists
        result = subprocess.run([
            "docker", "exec", "semantic_search_db", "psql", "-U", "postgres", "-d", "semantic_search", "-c",
            "SELECT COUNT(*) FROM imagenes;"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            for line in lines:
                line = line.strip()
                if line.isdigit():
                    print(f"   📊 Total records in IMAGENES view: {line}")
                    break
        
        # Check distribution by company
        result = subprocess.run([
            "docker", "exec", "semantic_search_db", "psql", "-U", "postgres", "-d", "semantic_search", "-c",
            "SELECT empresa, COUNT(*) FROM imagenes GROUP BY empresa ORDER BY empresa;"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print(f"   📊 Records per company in IMAGENES view:")
            lines = result.stdout.split('\n')
            for line in lines:
                if '|' in line and not line.startswith('-') and 'empresa' not in line:
                    print(f"      {line.strip()}")
                    
    except Exception as e:
        print(f"   ❌ Error checking IMAGENES view: {e}")

def check_colombia_table_specifically():
    """Detailed investigation of IMAGENES_COLOMBIA table"""
    print(f"\n🇨🇴 Detailed IMAGENES_COLOMBIA Investigation...")
    
    try:
        # Check table structure
        result = subprocess.run([
            "docker", "exec", "semantic_search_db", "psql", "-U", "postgres", "-d", "semantic_search", "-c",
            "\\d imagenes_colombia"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print(f"   📋 IMAGENES_COLOMBIA table structure:")
            lines = result.stdout.split('\n')
            for line in lines:
                if 'empresa' in line or 'idproducto' in line or 'embedding' in line or 'vector' in line:
                    print(f"      {line.strip()}")
        
        # Check all records with full details
        result = subprocess.run([
            "docker", "exec", "semantic_search_db", "psql", "-U", "postgres", "-d", "semantic_search", "-c",
            "SELECT empresa, idproducto, idphoto, urlphoto, LEFT(texto, 100) as texto_preview FROM imagenes_colombia ORDER BY idproducto, idphoto;"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print(f"   📋 All records in IMAGENES_COLOMBIA:")
            lines = result.stdout.split('\n')
            record_count = 0
            for line in lines:
                if '|' in line and not line.startswith('-') and 'empresa' not in line and line.strip():
                    print(f"      {line.strip()}")
                    record_count += 1
            print(f"   📊 Total records found: {record_count}")
                    
    except Exception as e:
        print(f"   ❌ Error investigating IMAGENES_COLOMBIA: {e}")

def test_multi_vendor_search():
    """Test multi-vendor search to see what companies are detected"""
    print(f"\n🔍 Testing Multi-Vendor Search Detection...")
    
    try:
        search_data = {
            "query_text": "test query",
            "limit": 10
        }
        
        response = requests.post(
            f"{BASE_URL}/api/v1/search/all-vendors",
            json=search_data,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            vendors = result.get('vendors_found', [])
            total_results = result.get('total_results', 0)
            
            print(f"   ✅ Multi-vendor search successful")
            print(f"   📊 Total results: {total_results}")
            print(f"   🏢 Vendors detected: {vendors}")
            
            # Show distribution
            results = result.get('results', [])
            vendor_counts = {}
            for res in results:
                vendor = res['empresa']
                vendor_counts[vendor] = vendor_counts.get(vendor, 0) + 1
            
            print(f"   📊 Results per vendor:")
            for vendor, count in vendor_counts.items():
                print(f"      - {vendor}: {count} results")
                
        else:
            print(f"   ❌ Multi-vendor search failed: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Error testing multi-vendor search: {e}")

def run_comprehensive_investigation():
    """Run comprehensive database investigation"""
    print("🕵️ Starting Comprehensive Database Investigation")
    print("=" * 60)
    
    # Step 1: Check API health
    if not check_api_health():
        print("❌ API is not healthy. Cannot proceed with investigation.")
        return False
    
    # Step 2: Investigate database structure
    tables = investigate_database_structure()
    
    # Step 3: Check specific tables
    for table in tables:
        check_specific_table_content(table)
    
    # Step 4: Check unified view
    check_imagenes_view()
    
    # Step 5: Detailed COLOMBIA investigation
    check_colombia_table_specifically()
    
    # Step 6: Test search functionality
    test_multi_vendor_search()
    
    print("\n" + "=" * 60)
    print("📊 INVESTIGATION SUMMARY")
    print("=" * 60)
    print("✅ Investigation completed. Check the detailed output above.")
    print("📋 Next steps:")
    print("   1. Review which table contains the test images")
    print("   2. Upload images specifically to COLOMBIA if needed")
    print("   3. Verify unified view is working correctly")
    
    return True

if __name__ == "__main__":
    success = run_comprehensive_investigation()
    exit(0 if success else 1)