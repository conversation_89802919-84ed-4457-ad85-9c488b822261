# Outputs para configuración mínima de Fargate

output "ecr_repository_url" {
  description = "URL del repositorio ECR"
  value       = aws_ecr_repository.app.repository_url
}

output "s3_bucket_name" {
  description = "Nombre del bucket S3 para uploads"
  value       = aws_s3_bucket.uploads.bucket
}

output "ecs_cluster_name" {
  description = "Nombre del cluster ECS"
  value       = aws_ecs_cluster.minimal.name
}

output "ecs_service_name" {
  description = "Nombre del servicio ECS"
  value       = aws_ecs_service.app.name
}

output "deployment_info" {
  description = "Información del despliegue"
  value = {
    ecr_repository = aws_ecr_repository.app.repository_url
    s3_bucket     = aws_s3_bucket.uploads.bucket
    ecs_cluster   = aws_ecs_cluster.minimal.name
    ecs_service   = aws_ecs_service.app.name
    region        = var.aws_region
    estimated_cost = "Ultra-low - Fargate Spot + S3 storage only"
    access_note   = "Application will be accessible via public IP on port 8000 (no load balancer)"
  }
}