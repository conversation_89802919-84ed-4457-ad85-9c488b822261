# Variables para configuración mínima de Fargate

variable "aws_region" {
  description = "AWS region for deployment"
  type        = string
  default     = "us-east-2"
}

variable "aws_profile" {
  description = "AWS CLI profile to use"
  type        = string
  default     = "IA"
}

variable "project_name" {
  description = "Name of the project (used for resource naming)"
  type        = string
  default     = "semantic-search-poc"
}

variable "database_url" {
  description = "PostgreSQL database connection URL"
  type        = string
  default     = "postgresql://postgres:<EMAIL>:5432/postgres"
}