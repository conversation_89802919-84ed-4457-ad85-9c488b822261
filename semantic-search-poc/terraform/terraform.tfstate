{"version": 4, "terraform_version": "1.12.2", "serial": 26, "lineage": "78668c65-374e-d131-b476-6ceb03770ca1", "outputs": {"deployment_info": {"value": {"access_note": "Application will be accessible via public IP on port 8000 (no load balancer)", "ecr_repository": "************.dkr.ecr.us-east-2.amazonaws.com/semantic-search-poc-app", "ecs_cluster": "semantic-search-poc-cluster", "ecs_service": "semantic-search-poc-service", "estimated_cost": "Ultra-low - Fargate Spot + S3 storage only", "region": "us-east-2", "s3_bucket": "semantic-search-poc-uploads-fbfa1itg"}, "type": ["object", {"access_note": "string", "ecr_repository": "string", "ecs_cluster": "string", "ecs_service": "string", "estimated_cost": "string", "region": "string", "s3_bucket": "string"}]}, "ecr_repository_url": {"value": "************.dkr.ecr.us-east-2.amazonaws.com/semantic-search-poc-app", "type": "string"}, "ecs_cluster_name": {"value": "semantic-search-poc-cluster", "type": "string"}, "ecs_service_name": {"value": "semantic-search-poc-service", "type": "string"}, "s3_bucket_name": {"value": "semantic-search-poc-uploads-fbfa1itg", "type": "string"}}, "resources": [{"mode": "data", "type": "aws_caller_identity", "name": "current", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"account_id": "************", "arn": "arn:aws:sts::************:assumed-role/AWSReservedSSO_IaPermissionSet_6bcba91af33ea105/juan.cubillos", "id": "************", "user_id": "AROAXNV2A6LASIV6Q7ZWD:juan.cubillos"}, "sensitive_attributes": [], "identity_schema_version": 0}]}, {"mode": "data", "type": "aws_subnets", "name": "default", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"filter": [{"name": "vpc-id", "values": ["vpc-02c6f99cde502f506"]}], "id": "us-east-2", "ids": ["subnet-0572b8522db8b2f8e", "subnet-0d9f1b5c510076333", "subnet-0f49b73cd8293ec3e"], "tags": null, "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0}]}, {"mode": "data", "type": "aws_vpc", "name": "default", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ec2:us-east-2:************:vpc/vpc-02c6f99cde502f506", "cidr_block": "**********/16", "cidr_block_associations": [{"association_id": "vpc-cidr-assoc-08954b5dddf9560c5", "cidr_block": "**********/16", "state": "associated"}], "default": true, "dhcp_options_id": "dopt-09188ecfc377f8139", "enable_dns_hostnames": true, "enable_dns_support": true, "enable_network_address_usage_metrics": false, "filter": null, "id": "vpc-02c6f99cde502f506", "instance_tenancy": "default", "ipv6_association_id": "", "ipv6_cidr_block": "", "main_route_table_id": "rtb-038b0b3231ba21173", "owner_id": "************", "state": null, "tags": {}, "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0}]}, {"mode": "managed", "type": "aws_cloudwatch_log_group", "name": "app", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:logs:us-east-2:************:log-group:/ecs/semantic-search-poc", "id": "/ecs/semantic-search-poc", "kms_key_id": "", "log_group_class": "STANDARD", "name": "/ecs/semantic-search-poc", "name_prefix": "", "retention_in_days": 1, "skip_destroy": false, "tags": {"Name": "semantic-search-poc-logs"}, "tags_all": {"Name": "semantic-search-poc-logs"}}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_ecr_repository", "name": "app", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ecr:us-east-2:************:repository/semantic-search-poc-app", "encryption_configuration": [{"encryption_type": "AES256", "kms_key": ""}], "force_delete": null, "id": "semantic-search-poc-app", "image_scanning_configuration": [{"scan_on_push": false}], "image_tag_mutability": "MUTABLE", "name": "semantic-search-poc-app", "registry_id": "************", "repository_url": "************.dkr.ecr.us-east-2.amazonaws.com/semantic-search-poc-app", "tags": {"Name": "semantic-search-poc-app"}, "tags_all": {"Name": "semantic-search-poc-app"}, "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiZGVsZXRlIjoxMjAwMDAwMDAwMDAwfX0="}]}, {"mode": "managed", "type": "aws_ecs_cluster", "name": "minimal", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ecs:us-east-2:************:cluster/semantic-search-poc-cluster", "configuration": [], "id": "arn:aws:ecs:us-east-2:************:cluster/semantic-search-poc-cluster", "name": "semantic-search-poc-cluster", "service_connect_defaults": [], "setting": [{"name": "containerInsights", "value": "disabled"}], "tags": {"Name": "semantic-search-poc-cluster"}, "tags_all": {"Name": "semantic-search-poc-cluster"}}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_ecs_service", "name": "app", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"alarms": [], "availability_zone_rebalancing": "DISABLED", "capacity_provider_strategy": [{"base": 1, "capacity_provider": "FARGATE_SPOT", "weight": 100}], "cluster": "arn:aws:ecs:us-east-2:************:cluster/semantic-search-poc-cluster", "deployment_circuit_breaker": [{"enable": false, "rollback": false}], "deployment_controller": [{"type": "ECS"}], "deployment_maximum_percent": 200, "deployment_minimum_healthy_percent": 100, "desired_count": 1, "enable_ecs_managed_tags": false, "enable_execute_command": false, "force_delete": null, "force_new_deployment": null, "health_check_grace_period_seconds": 0, "iam_role": "/aws-service-role/ecs.amazonaws.com/AWSServiceRoleForECS", "id": "arn:aws:ecs:us-east-2:************:service/semantic-search-poc-cluster/semantic-search-poc-service", "launch_type": "", "load_balancer": [], "name": "semantic-search-poc-service", "network_configuration": [{"assign_public_ip": true, "security_groups": ["sg-071256cf17ec50f26"], "subnets": ["subnet-0572b8522db8b2f8e", "subnet-0d9f1b5c510076333", "subnet-0f49b73cd8293ec3e"]}], "ordered_placement_strategy": [], "placement_constraints": [], "platform_version": "LATEST", "propagate_tags": "NONE", "scheduling_strategy": "REPLICA", "service_connect_configuration": [], "service_registries": [], "tags": {"Name": "semantic-search-poc-service"}, "tags_all": {"Name": "semantic-search-poc-service"}, "task_definition": "arn:aws:ecs:us-east-2:************:task-definition/semantic-search-poc-task:2", "timeouts": null, "triggers": {}, "volume_configuration": [], "vpc_lattice_configurations": [], "wait_for_steady_state": false}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_cloudwatch_log_group.app", "aws_ecr_repository.app", "aws_ecs_cluster.minimal", "aws_ecs_task_definition.app", "aws_iam_role.ecs_task_execution_role", "aws_iam_role.ecs_task_role", "aws_s3_bucket.uploads", "aws_security_group.fargate_minimal", "data.aws_subnets.default", "data.aws_vpc.default", "random_string.bucket_suffix"]}]}, {"mode": "managed", "type": "aws_ecs_task_definition", "name": "app", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ecs:us-east-2:************:task-definition/semantic-search-poc-task:2", "arn_without_revision": "arn:aws:ecs:us-east-2:************:task-definition/semantic-search-poc-task", "container_definitions": "[{\"environment\":[{\"name\":\"APP_HOST\",\"value\":\"0.0.0.0\"},{\"name\":\"APP_PORT\",\"value\":\"8000\"},{\"name\":\"AWS_REGION\",\"value\":\"us-east-2\"},{\"name\":\"DATABASE_URL\",\"value\":\"postgresql://postgres:<EMAIL>:5432/postgres\"},{\"name\":\"DEBUG\",\"value\":\"False\"},{\"name\":\"S3_BUCKET_NAME\",\"value\":\"semantic-search-poc-uploads-fbfa1itg\"},{\"name\":\"STORAGE_TYPE\",\"value\":\"s3\"}],\"essential\":true,\"image\":\"************.dkr.ecr.us-east-2.amazonaws.com/semantic-search-poc-app:latest\",\"logConfiguration\":{\"logDriver\":\"awslogs\",\"options\":{\"awslogs-group\":\"/ecs/semantic-search-poc\",\"awslogs-region\":\"us-east-2\",\"awslogs-stream-prefix\":\"ecs\"}},\"mountPoints\":[],\"name\":\"semantic-search-poc-container\",\"portMappings\":[{\"containerPort\":8000,\"hostPort\":8000,\"protocol\":\"tcp\"}],\"systemControls\":[],\"volumesFrom\":[]}]", "cpu": "256", "enable_fault_injection": false, "ephemeral_storage": [], "execution_role_arn": "arn:aws:iam::************:role/semantic-search-poc-ecs-execution-role", "family": "semantic-search-poc-task", "id": "semantic-search-poc-task", "inference_accelerator": [], "ipc_mode": "", "memory": "512", "network_mode": "awsvpc", "pid_mode": "", "placement_constraints": [], "proxy_configuration": [], "requires_compatibilities": ["FARGATE"], "revision": 2, "runtime_platform": [], "skip_destroy": false, "tags": {"Name": "semantic-search-poc-task"}, "tags_all": {"Name": "semantic-search-poc-task"}, "task_role_arn": "arn:aws:iam::************:role/semantic-search-poc-ecs-task-role", "track_latest": false, "volume": []}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjEifQ==", "dependencies": ["aws_cloudwatch_log_group.app", "aws_ecr_repository.app", "aws_iam_role.ecs_task_execution_role", "aws_iam_role.ecs_task_role", "aws_s3_bucket.uploads", "random_string.bucket_suffix"]}]}, {"mode": "managed", "type": "aws_iam_role", "name": "ecs_task_execution_role", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:role/semantic-search-poc-ecs-execution-role", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeRole\",\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"ecs-tasks.amazonaws.com\"}}],\"Version\":\"2012-10-17\"}", "create_date": "2025-06-19T21:32:17Z", "description": "", "force_detach_policies": false, "id": "semantic-search-poc-ecs-execution-role", "inline_policy": [], "managed_policy_arns": ["arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"], "max_session_duration": 3600, "name": "semantic-search-poc-ecs-execution-role", "name_prefix": "", "path": "/", "permissions_boundary": "", "tags": {}, "tags_all": {}, "unique_id": "AROAXNV2A6LAU7CDMB6G4"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_iam_role", "name": "ecs_task_role", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:role/semantic-search-poc-ecs-task-role", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeRole\",\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"ecs-tasks.amazonaws.com\"}}],\"Version\":\"2012-10-17\"}", "create_date": "2025-06-09T16:22:00Z", "description": "", "force_detach_policies": false, "id": "semantic-search-poc-ecs-task-role", "inline_policy": [{"name": "semantic-search-poc-bedrock-policy", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"bedrock:InvokeModel\",\"bedrock:InvokeModelWithResponseStream\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:bedrock:us-east-2::foundation-model/amazon.nova-micro-v1:0\",\"arn:aws:bedrock:us-east-2::foundation-model/amazon.nova-lite-v1:0\",\"arn:aws:bedrock:us-east-2::foundation-model/amazon.titan-embed-text-v1\",\"arn:aws:bedrock:us-east-2::foundation-model/amazon.titan-embed-text-v2:0\",\"arn:aws:bedrock:us-east-2:************:inference-profile/us.amazon.nova-lite-v1:0\",\"arn:aws:bedrock:us-east-2:************:inference-profile/us.amazon.nova-micro-v1:0\"]}]}"}, {"name": "semantic-search-poc-s3-policy", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"s3:GetObject\",\"s3:PutObject\",\"s3:DeleteObject\",\"s3:PutObjectAcl\",\"s3:ListBucket\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:s3:::semantic-search-poc-uploads-fbfa1itg\",\"arn:aws:s3:::semantic-search-poc-uploads-fbfa1itg/*\"]}]}"}], "managed_policy_arns": [], "max_session_duration": 3600, "name": "semantic-search-poc-ecs-task-role", "name_prefix": "", "path": "/", "permissions_boundary": "", "tags": {}, "tags_all": {}, "unique_id": "AROAXNV2A6LASWVJDA7NZ"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjAifQ=="}]}, {"mode": "managed", "type": "aws_iam_role_policy", "name": "ecs_task_bedrock", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "semantic-search-poc-ecs-task-role:semantic-search-poc-bedrock-policy", "name": "semantic-search-poc-bedrock-policy", "name_prefix": "", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"bedrock:InvokeModel\",\"bedrock:InvokeModelWithResponseStream\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:bedrock:us-east-2::foundation-model/amazon.nova-micro-v1:0\",\"arn:aws:bedrock:us-east-2::foundation-model/amazon.nova-lite-v1:0\",\"arn:aws:bedrock:us-east-2::foundation-model/amazon.titan-embed-text-v1\",\"arn:aws:bedrock:us-east-2::foundation-model/amazon.titan-embed-text-v2:0\",\"arn:aws:bedrock:us-east-1::foundation-model/amazon.nova-lite-v1:0\",\"arn:aws:bedrock:us-west-2::foundation-model/amazon.nova-lite-v1:0\",\"arn:aws:bedrock:us-east-2:************:inference-profile/us.amazon.nova-lite-v1:0\",\"arn:aws:bedrock:us-east-2:************:inference-profile/us.amazon.nova-micro-v1:0\"]}]}", "role": "semantic-search-poc-ecs-task-role"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["aws_iam_role.ecs_task_role", "data.aws_caller_identity.current"]}]}, {"mode": "managed", "type": "aws_iam_role_policy", "name": "ecs_task_s3", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "semantic-search-poc-ecs-task-role:semantic-search-poc-s3-policy", "name": "semantic-search-poc-s3-policy", "name_prefix": "", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"s3:GetObject\",\"s3:PutObject\",\"s3:DeleteObject\",\"s3:PutObjectAcl\",\"s3:ListBucket\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:s3:::semantic-search-poc-uploads-fbfa1itg\",\"arn:aws:s3:::semantic-search-poc-uploads-fbfa1itg/*\"]}]}", "role": "semantic-search-poc-ecs-task-role"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["aws_iam_role.ecs_task_role", "aws_s3_bucket.uploads", "random_string.bucket_suffix"]}]}, {"mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "ecs_task_execution_role", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "semantic-search-poc-ecs-execution-role-20250619213222333100000002", "policy_arn": "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy", "role": "semantic-search-poc-ecs-execution-role"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["aws_iam_role.ecs_task_execution_role"]}]}, {"mode": "managed", "type": "aws_s3_bucket", "name": "uploads", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"acceleration_status": "", "acl": null, "arn": "arn:aws:s3:::semantic-search-poc-uploads-fbfa1itg", "bucket": "semantic-search-poc-uploads-fbfa1itg", "bucket_domain_name": "semantic-search-poc-uploads-fbfa1itg.s3.amazonaws.com", "bucket_prefix": "", "bucket_regional_domain_name": "semantic-search-poc-uploads-fbfa1itg.s3.us-east-2.amazonaws.com", "cors_rule": [], "force_destroy": false, "grant": [{"id": "41ddc67b0582200ce86e6eab98333d7c7e5a561b9ecaa64510c34d7065aadd58", "permissions": ["FULL_CONTROL"], "type": "CanonicalUser", "uri": ""}], "hosted_zone_id": "Z2O1EMRO9K5GLX", "id": "semantic-search-poc-uploads-fbfa1itg", "lifecycle_rule": [], "logging": [], "object_lock_configuration": [], "object_lock_enabled": false, "policy": "{\"Statement\":[{\"Action\":\"s3:GetObject\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"arn:aws:s3:::semantic-search-poc-uploads-fbfa1itg/*\",\"Sid\":\"PublicReadGetObject\"}],\"Version\":\"2012-10-17\"}", "region": "us-east-2", "replication_configuration": [], "request_payer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "server_side_encryption_configuration": [{"rule": [{"apply_server_side_encryption_by_default": [{"kms_master_key_id": "", "sse_algorithm": "AES256"}], "bucket_key_enabled": false}]}], "tags": {}, "tags_all": {}, "timeouts": null, "versioning": [{"enabled": false, "mfa_delete": false}], "website": [], "website_domain": null, "website_endpoint": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjM2MDAwMDAwMDAwMDAsInJlYWQiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["random_string.bucket_suffix"]}]}, {"mode": "managed", "type": "aws_s3_bucket_policy", "name": "uploads", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"bucket": "semantic-search-poc-uploads-fbfa1itg", "id": "semantic-search-poc-uploads-fbfa1itg", "policy": "{\"Statement\":[{\"Action\":\"s3:GetObject\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"arn:aws:s3:::semantic-search-poc-uploads-fbfa1itg/*\",\"Sid\":\"PublicReadGetObject\"}],\"Version\":\"2012-10-17\"}"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["aws_s3_bucket.uploads", "aws_s3_bucket_public_access_block.uploads", "random_string.bucket_suffix"]}]}, {"mode": "managed", "type": "aws_s3_bucket_public_access_block", "name": "uploads", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"block_public_acls": false, "block_public_policy": false, "bucket": "semantic-search-poc-uploads-fbfa1itg", "id": "semantic-search-poc-uploads-fbfa1itg", "ignore_public_acls": false, "restrict_public_buckets": false}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["aws_s3_bucket.uploads", "random_string.bucket_suffix"]}]}, {"mode": "managed", "type": "aws_security_group", "name": "fargate_minimal", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-2:************:security-group/sg-071256cf17ec50f26", "description": "Managed by Terraform", "egress": [{"cidr_blocks": ["0.0.0.0/0"], "description": "", "from_port": 0, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "-1", "security_groups": [], "self": false, "to_port": 0}], "id": "sg-071256cf17ec50f26", "ingress": [{"cidr_blocks": ["0.0.0.0/0"], "description": "", "from_port": 8000, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": [], "self": false, "to_port": 8000}], "name": "semantic-search-poc-fargate-20250619213220038000000001", "name_prefix": "semantic-search-poc-fargate-", "owner_id": "************", "revoke_rules_on_delete": false, "tags": {"Name": "semantic-search-poc-fargate-sg"}, "tags_all": {"Name": "semantic-search-poc-fargate-sg"}, "timeouts": null, "vpc_id": "vpc-02c6f99cde502f506"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6OTAwMDAwMDAwMDAwfSwic2NoZW1hX3ZlcnNpb24iOiIxIn0=", "dependencies": ["data.aws_vpc.default"]}]}, {"mode": "managed", "type": "random_string", "name": "bucket_suffix", "provider": "provider[\"registry.terraform.io/hashicorp/random\"]", "instances": [{"schema_version": 2, "attributes": {"id": "fbfa1itg", "keepers": null, "length": 8, "lower": true, "min_lower": 0, "min_numeric": 0, "min_special": 0, "min_upper": 0, "number": true, "numeric": true, "override_special": null, "result": "fbfa1itg", "special": false, "upper": false}, "sensitive_attributes": [], "identity_schema_version": 0}]}], "check_results": null}