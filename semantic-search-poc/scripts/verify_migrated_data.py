#!/usr/bin/env python3
"""
Verify Migrated Data Script

Script para verificar que los productos migrados están realmente almacenados
en la base de datos de búsqueda semántica.
"""

import sys
import os
import logging
from sqlalchemy import create_engine, text
from dotenv import load_dotenv

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'app'))

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def verify_database_connection():
    """
    Verificar la conexión a la base de datos y mostrar detalles
    """
    print("🔍 Verificando configuración de base de datos...")
    
    # Get database URL from environment
    database_url = os.getenv("DATABASE_URL", "postgresql://postgres:postgres@localhost:5432/semantic_search")
    print(f"📍 Database URL: {database_url}")
    
    try:
        # Create engine and test connection
        engine = create_engine(database_url)
        
        with engine.connect() as conn:
            # Test basic connection
            result = conn.execute(text("SELECT version()"))
            version = result.scalar()
            print(f"✅ Conexión exitosa a PostgreSQL")
            print(f"   Versión: {version}")
            
            # Get current database name
            result = conn.execute(text("SELECT current_database()"))
            current_db = result.scalar()
            print(f"   Base de datos actual: {current_db}")
            
            # Get current schema
            result = conn.execute(text("SELECT current_schema()"))
            current_schema = result.scalar()
            print(f"   Schema actual: {current_schema}")
            
            return engine
            
    except Exception as e:
        print(f"❌ Error conectando a la base de datos: {e}")
        return None

def check_semantic_search_tables(engine):
    """
    Verificar qué tablas existen para búsqueda semántica
    """
    print("\n📋 Verificando tablas de búsqueda semántica...")
    
    try:
        with engine.connect() as conn:
            # List all tables that start with 'imagenes_'
            result = conn.execute(text("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name LIKE 'imagenes_%'
                ORDER BY table_name
            """))
            
            tables = result.fetchall()
            
            if tables:
                print(f"✅ Encontradas {len(tables)} tablas de búsqueda semántica:")
                for table in tables:
                    print(f"   - {table.table_name}")
                return [table.table_name for table in tables]
            else:
                print("❌ No se encontraron tablas de búsqueda semántica")
                return []
                
    except Exception as e:
        print(f"❌ Error verificando tablas: {e}")
        return []

def verify_migrated_products(engine, table_name="imagenes_dropi_colombia"):
    """
    Verificar los productos migrados en la tabla específica
    """
    print(f"\n🔍 Verificando productos en tabla '{table_name}'...")
    
    try:
        with engine.connect() as conn:
            # Check if table exists
            result = conn.execute(text("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = :table_name
                )
            """), {"table_name": table_name})
            
            table_exists = result.scalar()
            
            if not table_exists:
                print(f"❌ La tabla '{table_name}' no existe")
                return False
            
            print(f"✅ La tabla '{table_name}' existe")
            
            # Get table structure
            print(f"\n📋 Estructura de la tabla '{table_name}':")
            result = conn.execute(text(f"""
                SELECT column_name, data_type, is_nullable
                FROM information_schema.columns 
                WHERE table_name = '{table_name}' 
                AND table_schema = 'public'
                ORDER BY ordinal_position
            """))
            
            for row in result:
                print(f"   - {row.column_name}: {row.data_type} ({'NULL' if row.is_nullable == 'YES' else 'NOT NULL'})")
            
            # Count total records
            result = conn.execute(text(f'SELECT COUNT(*) FROM "{table_name}"'))
            total_count = result.scalar()
            print(f"\n📊 Total de registros: {total_count}")
            
            if total_count == 0:
                print("❌ No hay datos en la tabla")
                return False
            
            # Show sample of migrated products
            print(f"\n🛍️ Muestra de productos migrados:")
            result = conn.execute(text(f"""
                SELECT "idProducto", "idPhoto", "urlPhoto", 
                       LEFT("texto", 100) as descripcion_corta,
                       "empresa"
                FROM "{table_name}" 
                ORDER BY "idProducto", "idPhoto"
                LIMIT 10
            """))
            
            for row in result:
                print(f"   - Producto {row.idProducto}, Foto {row.idPhoto}: {row.descripcion_corta}...")
                print(f"     URL: {row.urlPhoto}")
                print(f"     Empresa: {row.empresa}")
                print()
            
            # Check for our specific migrated products (IDs: 1, 2, 3, 4, 5, 6, 7, 9)
            migrated_ids = [1, 2, 3, 4, 5, 6, 7, 9]
            print(f"🎯 Verificando productos específicos migrados (IDs: {migrated_ids}):")
            
            for product_id in migrated_ids:
                result = conn.execute(text(f"""
                    SELECT COUNT(*) FROM "{table_name}" 
                    WHERE "idProducto" = :product_id
                """), {"product_id": product_id})
                
                count = result.scalar()
                status = "✅" if count > 0 else "❌"
                print(f"   {status} Producto {product_id}: {count} registros")
            
            # Get unique companies
            result = conn.execute(text(f'SELECT DISTINCT "empresa" FROM "{table_name}"'))
            companies = result.fetchall()
            print(f"\n🏢 Empresas en la tabla: {[c.empresa for c in companies]}")
            
            return True
            
    except Exception as e:
        print(f"❌ Error verificando productos: {e}")
        return False

def test_search_functionality(engine, table_name="imagenes_dropi_colombia"):
    """
    Probar la funcionalidad de búsqueda
    """
    print(f"\n🔍 Probando funcionalidad de búsqueda en '{table_name}'...")
    
    try:
        with engine.connect() as conn:
            # Check if pgvector extension is available
            result = conn.execute(text("""
                SELECT EXISTS(
                    SELECT 1 FROM pg_extension WHERE extname = 'vector'
                )
            """))
            
            has_vector = result.scalar()
            print(f"   pgvector extension: {'✅ Disponible' if has_vector else '❌ No disponible'}")
            
            # Check if embedding column exists and has data
            result = conn.execute(text(f"""
                SELECT COUNT(*) FROM "{table_name}" 
                WHERE "embedding" IS NOT NULL
            """))
            
            embedding_count = result.scalar()
            print(f"   Registros con embeddings: {embedding_count}")
            
            if embedding_count > 0:
                print("✅ La tabla tiene embeddings y está lista para búsqueda semántica")
            else:
                print("❌ No hay embeddings en la tabla")
            
    except Exception as e:
        print(f"❌ Error probando búsqueda: {e}")

def main():
    """
    Función principal de verificación
    """
    print("🔍 VERIFICACIÓN DE DATOS MIGRADOS")
    print("=" * 50)
    
    # Step 1: Verify database connection
    engine = verify_database_connection()
    if not engine:
        return
    
    # Step 2: Check semantic search tables
    tables = check_semantic_search_tables(engine)
    
    # Step 3: Verify migrated products
    target_table = "imagenes_dropi_colombia"
    if target_table in tables:
        verify_migrated_products(engine, target_table)
        test_search_functionality(engine, target_table)
    else:
        print(f"\n❌ La tabla objetivo '{target_table}' no fue encontrada")
        if tables:
            print("Tablas disponibles:")
            for table in tables:
                print(f"   - {table}")
                verify_migrated_products(engine, table)
    
    print("\n" + "=" * 50)
    print("🏁 VERIFICACIÓN COMPLETADA")

if __name__ == "__main__":
    main()
