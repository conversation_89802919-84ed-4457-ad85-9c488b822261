#!/usr/bin/env python3
"""
Dual Profile Migration Script

Script que maneja la migración usando dos perfiles de AWS:
- Perfil DEV: Para extraer datos de la base de datos de desarrollo
- Perfil IA: Para servicios de AWS Bedrock y almacenamiento en base de datos semántica
"""

import sys
import os
import asyncio
import json
import logging
from datetime import datetime

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'app'))

from database.dev_database import ProductData, ProductPhotoData, ProductWithPhotos
from services.embedding_service import embedding_service
from services.llm_service import llm_service
from utils.validation import data_validator
from utils.cloudfront import cloudfront_generator

# Import db_manager directly
import importlib.util
db_module_path = os.path.join(os.path.dirname(__file__), '..', 'app', 'database.py')
spec = importlib.util.spec_from_file_location("database", db_module_path)
database_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(database_module)
db_manager = database_module.db_manager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def verify_aws_profiles():
    """
    Verificar que ambos perfiles de AWS estén configurados correctamente
    """
    print("🔍 Verificando perfiles de AWS...")
    
    import boto3
    
    # Test DEV profile for database access
    try:
        session_dev = boto3.Session(profile_name='DEV')
        sts_dev = session_dev.client('sts', region_name='us-east-2')
        identity_dev = sts_dev.get_caller_identity()
        print(f"✅ Perfil DEV: {identity_dev['Arn']}")
    except Exception as e:
        print(f"❌ Error con perfil DEV: {e}")
        return False
    
    # Test IA profile for AI services
    try:
        session_ia = boto3.Session(profile_name='IA')
        sts_ia = session_ia.client('sts', region_name='us-east-2')
        identity_ia = sts_ia.get_caller_identity()
        print(f"✅ Perfil IA: {identity_ia['Arn']}")
    except Exception as e:
        print(f"❌ Error con perfil IA: {e}")
        return False
    
    return True

def extract_products_with_dev_profile(sample_size: int = 10):
    """
    Extraer productos usando el perfil DEV
    """
    print(f"📦 Extrayendo {sample_size} productos con perfil DEV...")
    
    # Import here to ensure DEV profile is used
    from database.dev_database import dev_db_manager
    
    # Test connection
    if not dev_db_manager.test_connection():
        print("❌ No se puede conectar a la base de datos de desarrollo")
        return []
    
    # Extract products
    products = dev_db_manager.extract_products_batch(offset=0, limit=sample_size)
    
    # Filter to only products with S3 photos
    products_with_s3 = []
    for product in products:
        s3_photos = [photo for photo in product.photos if photo.url_s3]
        if s3_photos:
            product.photos = s3_photos
            products_with_s3.append(product)
    
    print(f"✅ Extraídos {len(products_with_s3)} productos con fotos S3")
    return products_with_s3

async def process_products_with_ia_profile(products, company_name="mexico"):
    """
    Procesar productos usando el perfil IA para servicios de Bedrock
    """
    print(f"🧠 Procesando productos con perfil IA (servicios de Bedrock)...")
    
    # Verify target database connection
    if not db_manager.test_connection():
        print("❌ No se puede conectar a la base de datos objetivo")
        return False
    
    # Create company table if needed
    if not db_manager.create_company_table(company_name):
        print("❌ No se pudo crear la tabla de empresa")
        return False
    
    print(f"✅ Tabla '{company_name}' lista")
    
    successful_products = 0
    total_photos_processed = 0
    
    for i, product in enumerate(products, 1):
        print(f"\n--- Producto {i}/{len(products)}: {product.product.name} ---")
        
        try:
            # Validate product
            validation = data_validator.validate_product_data(product.product)
            if not validation.is_valid:
                print(f"⚠️ Producto {product.product.id} falló validación: {validation.errors}")
                continue
            
            # Process each photo
            photos_stored = 0
            for photo in product.photos:
                try:
                    # Generate CloudFront URL
                    s3_url = f"https://dropi-core-colombia-dev.s3.us-east-2.amazonaws.com/{photo.url_s3}"
                    cloudfront_url = cloudfront_generator.convert_s3_url_to_cloudfront(s3_url)
                    final_url = cloudfront_url if cloudfront_url else s3_url
                    
                    print(f"📸 URL CloudFront: {final_url[:80]}...")
                    
                    # Generate enhanced description using IA profile (Bedrock)
                    enhanced_description = llm_service.generate_enhanced_product_description(
                        product_name=product.product.name,
                        supplier_description=product.product.description,
                        dropi_description=product.product.dropi_app_description,
                        image_bytes=None,
                        price=product.product.sale_price
                    )
                    
                    if not enhanced_description:
                        # Fallback description
                        descriptions = []
                        if product.product.name:
                            descriptions.append(product.product.name)
                        if product.product.description:
                            clean_desc = product.product.description.replace('<p>', '').replace('</p>', '').replace('<br>', ' ')
                            descriptions.append(clean_desc)
                        enhanced_description = " ".join(descriptions) if descriptions else "Producto sin descripción"
                    
                    print(f"📝 Descripción: {enhanced_description[:100]}...")
                    
                    # Generate embedding using IA profile (Bedrock)
                    embedding = embedding_service.get_embedding(enhanced_description)
                    if embedding is None:
                        print(f"❌ No se pudo generar embedding para producto {product.product.id}")
                        continue
                    
                    # Store in database
                    success = db_manager.insert_image(
                        company_name=company_name,
                        id_producto=product.product.id,
                        id_photo=photo.id,
                        url_photo=final_url,
                        texto=enhanced_description,
                        embedding=embedding
                    )
                    
                    if success:
                        photos_stored += 1
                        total_photos_processed += 1
                        print(f"✅ Foto {photo.id} almacenada exitosamente")
                    else:
                        print(f"❌ Error almacenando foto {photo.id}")
                        
                except Exception as e:
                    print(f"❌ Error procesando foto {photo.id}: {e}")
            
            if photos_stored > 0:
                successful_products += 1
                print(f"✅ Producto {product.product.id} migrado exitosamente ({photos_stored} fotos)")
            else:
                print(f"⚠️ Producto {product.product.id} no se pudo migrar")
                    
        except Exception as e:
            print(f"❌ Error procesando producto {product.product.id}: {e}")
    
    print(f"\n🎉 Migración completada:")
    print(f"  - Productos exitosos: {successful_products}/{len(products)}")
    print(f"  - Fotos procesadas: {total_photos_processed}")
    
    return successful_products > 0

async def test_semantic_search(company_name="mexico"):
    """
    Probar la búsqueda semántica con perfil IA
    """
    print(f"\n🔍 Probando búsqueda semántica en tabla '{company_name}'...")
    
    test_queries = [
        "reloj deportivo",
        "televisor Sony",
        "laptop computadora",
        "equipo de sonido"
    ]
    
    for query in test_queries:
        try:
            # Generate embedding using IA profile
            query_embedding = embedding_service.get_embedding(query)
            if query_embedding is not None:
                results = db_manager.search_similar_images(
                    company_name=company_name,
                    query_embedding=query_embedding,
                    limit=3
                )
                
                print(f"  📍 '{query}': {len(results)} resultados")
                for j, result in enumerate(results[:2], 1):
                    print(f"    {j}. Producto {result.get('idproducto')} - Distancia: {result.get('distancia_coseno', 0):.3f}")
        except Exception as e:
            print(f"Error en búsqueda '{query}': {e}")

async def main():
    """
    Función principal que coordina la migración con perfiles duales
    """
    print("🚀 MIGRACIÓN CON PERFILES DUALES DE AWS")
    print("=" * 60)
    print("📋 Perfil DEV: Extracción de datos de desarrollo")
    print("📋 Perfil IA: Servicios de Bedrock y almacenamiento")
    print("=" * 60)
    
    try:
        # Step 1: Verify AWS profiles
        if not verify_aws_profiles():
            print("❌ Error en verificación de perfiles AWS")
            return
        
        # Step 2: Extract products using DEV profile
        products = extract_products_with_dev_profile(sample_size=10)
        if not products:
            print("❌ No se pudieron extraer productos")
            return
        
        # Step 3: Process products using IA profile
        success = await process_products_with_ia_profile(products, company_name="mexico")
        if not success:
            print("❌ Error en procesamiento con perfil IA")
            return
        
        # Step 4: Test semantic search
        await test_semantic_search(company_name="mexico")
        
        print("\n✅ Migración dual-profile completada exitosamente")
        print("🎯 Sistema listo con CloudFront y búsqueda semántica")
        
    except Exception as e:
        print(f"❌ Error en migración: {e}")
        logger.error(f"Migration error: {e}")

if __name__ == "__main__":
    asyncio.run(main())
