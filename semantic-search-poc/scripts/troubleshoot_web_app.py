#!/usr/bin/env python3
"""
Troubleshoot Web Application Script

Script para diagnosticar problemas con la aplicación web de búsqueda semántica
y verificar que todos los componentes estén funcionando correctamente.
"""

import sys
import os
import requests
import json
from sqlalchemy import create_engine, text
from dotenv import load_dotenv

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'app'))

from services.embedding_service import embedding_service

# Load environment variables
load_dotenv()

def test_database_connection():
    """
    Verificar la conexión de la aplicación web a la base de datos
    """
    print("🔍 VERIFICANDO CONEXIÓN DE BASE DE DATOS")
    print("=" * 50)
    
    database_url = os.getenv("DATABASE_URL")
    print(f"📍 Database URL: {database_url}")
    
    try:
        engine = create_engine(database_url)
        
        with engine.connect() as conn:
            # Test basic connection
            result = conn.execute(text("SELECT current_database(), current_user"))
            db_info = result.fetchone()
            print(f"✅ Conectado a base de datos: {db_info[0]}")
            print(f"✅ Usuario actual: {db_info[1]}")
            
            # Check if imagenes_mexico table exists
            result = conn.execute(text("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = 'imagenes_mexico'
                )
            """))
            
            table_exists = result.scalar()
            print(f"✅ Tabla 'imagenes_mexico' existe: {table_exists}")
            
            if table_exists:
                # Count records in mexico table
                result = conn.execute(text("SELECT COUNT(*) FROM imagenes_mexico"))
                count = result.scalar()
                print(f"✅ Registros en 'imagenes_mexico': {count}")
                
                # Show sample data
                result = conn.execute(text("""
                    SELECT idproducto, LEFT(texto, 50) as descripcion_corta, empresa
                    FROM imagenes_mexico 
                    LIMIT 5
                """))
                
                print(f"\n📋 Muestra de datos en 'imagenes_mexico':")
                for row in result:
                    print(f"   - Producto {row.idproducto}: {row.descripcion_corta}... (Empresa: {row.empresa})")
            
            return True
            
    except Exception as e:
        print(f"❌ Error de conexión: {e}")
        return False

def test_web_application_endpoints():
    """
    Probar los endpoints de la aplicación web
    """
    print(f"\n🌐 PROBANDO ENDPOINTS DE LA APLICACIÓN WEB")
    print("=" * 50)
    
    base_url = "http://*************:8000"
    
    # Test health endpoint
    try:
        response = requests.get(f"{base_url}/health", timeout=10)
        print(f"✅ Health endpoint: {response.status_code}")
        if response.status_code == 200:
            health_data = response.json()
            print(f"   Status: {health_data.get('status')}")
            print(f"   Database connected: {health_data.get('database_connected')}")
        else:
            print(f"   Response: {response.text}")
    except Exception as e:
        print(f"❌ Error en health endpoint: {e}")
    
    # Test companies endpoint
    try:
        response = requests.get(f"{base_url}/api/v1/companies", timeout=10)
        print(f"✅ Companies endpoint: {response.status_code}")
        if response.status_code == 200:
            companies = response.json()
            print(f"   Empresas disponibles: {companies}")
        else:
            print(f"   Response: {response.text}")
    except Exception as e:
        print(f"❌ Error en companies endpoint: {e}")
    
    # Test search endpoint with mexico company
    try:
        search_data = {
            "company_name": "mexico",
            "query": "televisor",
            "limit": 5
        }
        
        response = requests.post(
            f"{base_url}/api/v1/search", 
            json=search_data,
            timeout=15
        )
        print(f"✅ Search endpoint: {response.status_code}")
        if response.status_code == 200:
            search_results = response.json()
            print(f"   Resultados encontrados: {len(search_results.get('results', []))}")
            for i, result in enumerate(search_results.get('results', [])[:3]):
                print(f"   {i+1}. Producto {result.get('idProducto')}: {result.get('texto', '')[:50]}...")
        else:
            print(f"   Response: {response.text}")
    except Exception as e:
        print(f"❌ Error en search endpoint: {e}")

def test_embedding_service():
    """
    Probar el servicio de embeddings
    """
    print(f"\n🧠 PROBANDO SERVICIO DE EMBEDDINGS")
    print("=" * 50)
    
    try:
        # Test embedding generation
        test_query = "televisor Sony"
        embedding = embedding_service.get_embedding(test_query)
        
        if embedding is not None:
            print(f"✅ Embedding generado para '{test_query}'")
            print(f"   Dimensiones: {embedding.shape}")
            print(f"   Tipo: {type(embedding)}")
            print(f"   Primeros 5 valores: {embedding[:5]}")
            return embedding
        else:
            print(f"❌ No se pudo generar embedding para '{test_query}'")
            return None
            
    except Exception as e:
        print(f"❌ Error en servicio de embeddings: {e}")
        return None

def test_direct_database_search():
    """
    Probar búsqueda directa en la base de datos
    """
    print(f"\n🔍 PROBANDO BÚSQUEDA DIRECTA EN BASE DE DATOS")
    print("=" * 50)
    
    # Generate embedding for search
    test_embedding = test_embedding_service()
    if test_embedding is None:
        print("❌ No se puede probar búsqueda sin embedding")
        return
    
    try:
        database_url = os.getenv("DATABASE_URL")
        engine = create_engine(database_url)
        
        with engine.connect() as conn:
            # Test similarity search
            embedding_str = '[' + ','.join(map(str, test_embedding)) + ']'
            
            query = text("""
                SELECT 
                    idproducto,
                    idphoto,
                    LEFT(texto, 100) as descripcion_corta,
                    urlphoto,
                    empresa,
                    1 - (embedding <=> :query_embedding::vector) as similarity
                FROM imagenes_mexico
                WHERE embedding IS NOT NULL
                ORDER BY embedding <=> :query_embedding::vector
                LIMIT 5
            """)
            
            result = conn.execute(query, {"query_embedding": embedding_str})
            
            print(f"✅ Búsqueda directa ejecutada")
            results = result.fetchall()
            
            if results:
                print(f"   Encontrados {len(results)} resultados:")
                for i, row in enumerate(results):
                    print(f"   {i+1}. Producto {row.idproducto} (Similarity: {row.similarity:.3f})")
                    print(f"      Descripción: {row.descripcion_corta}...")
                    print(f"      URL: {row.urlphoto[:60]}...")
                    print()
            else:
                print("❌ No se encontraron resultados")
                
    except Exception as e:
        print(f"❌ Error en búsqueda directa: {e}")

def test_specific_product_searches():
    """
    Probar búsquedas específicas de los productos migrados
    """
    print(f"\n🎯 PROBANDO BÚSQUEDAS ESPECÍFICAS DE PRODUCTOS MIGRADOS")
    print("=" * 50)
    
    base_url = "http://*************:8000"
    
    test_queries = [
        "hielera mágica",
        "juguete perro",
        "reloj militar",
        "cama gato",
        "televisor Sony",
        "laptop Lenovo",
        "equipo sonido"
    ]
    
    for query in test_queries:
        try:
            search_data = {
                "company_name": "mexico",
                "query": query,
                "limit": 3
            }
            
            response = requests.post(
                f"{base_url}/api/v1/search", 
                json=search_data,
                timeout=15
            )
            
            if response.status_code == 200:
                search_results = response.json()
                results_count = len(search_results.get('results', []))
                print(f"✅ '{query}': {results_count} resultados")
                
                if results_count > 0:
                    best_result = search_results['results'][0]
                    print(f"   Mejor resultado: Producto {best_result.get('idProducto')}")
                    print(f"   Distancia: {best_result.get('distancia_coseno', 'N/A')}")
            else:
                print(f"❌ '{query}': Error {response.status_code}")
                
        except Exception as e:
            print(f"❌ '{query}': Error - {e}")

def check_table_configuration():
    """
    Verificar configuración de tablas en la aplicación
    """
    print(f"\n📋 VERIFICANDO CONFIGURACIÓN DE TABLAS")
    print("=" * 50)
    
    try:
        database_url = os.getenv("DATABASE_URL")
        engine = create_engine(database_url)
        
        with engine.connect() as conn:
            # List all imagenes_ tables
            result = conn.execute(text("""
                SELECT 
                    table_name,
                    (SELECT COUNT(*) FROM information_schema.columns 
                     WHERE table_name = t.table_name AND column_name = 'embedding') as has_embedding,
                    (SELECT COUNT(*) FROM information_schema.columns 
                     WHERE table_name = t.table_name AND column_name = 'empresa') as has_empresa
                FROM information_schema.tables t
                WHERE table_schema = 'public' 
                AND table_name LIKE 'imagenes_%'
                ORDER BY table_name
            """))
            
            print("📊 Tablas de búsqueda semántica disponibles:")
            for row in result:
                # Get record count for each table
                count_result = conn.execute(text(f"SELECT COUNT(*) FROM {row.table_name}"))
                count = count_result.scalar()
                
                print(f"   - {row.table_name}: {count} registros")
                print(f"     Embedding: {'✅' if row.has_embedding else '❌'}")
                print(f"     Empresa: {'✅' if row.has_empresa else '❌'}")
                
                if row.table_name == 'imagenes_mexico':
                    # Check empresa values in mexico table
                    empresa_result = conn.execute(text("""
                        SELECT DISTINCT empresa, COUNT(*) 
                        FROM imagenes_mexico 
                        GROUP BY empresa
                    """))
                    print(f"     Empresas en tabla: {[f'{r[0]} ({r[1]})' for r in empresa_result]}")
                
                print()
                
    except Exception as e:
        print(f"❌ Error verificando configuración: {e}")

def main():
    """
    Función principal de troubleshooting
    """
    print("🔧 TROUBLESHOOTING DE APLICACIÓN WEB SEMÁNTICA")
    print("=" * 60)
    
    # Step 1: Test database connection
    if not test_database_connection():
        print("❌ Problema crítico con la base de datos")
        return
    
    # Step 2: Check table configuration
    check_table_configuration()
    
    # Step 3: Test embedding service
    test_embedding_service()
    
    # Step 4: Test direct database search
    test_direct_database_search()
    
    # Step 5: Test web application endpoints
    test_web_application_endpoints()
    
    # Step 6: Test specific product searches
    test_specific_product_searches()
    
    print("\n" + "=" * 60)
    print("🏁 TROUBLESHOOTING COMPLETADO")

if __name__ == "__main__":
    main()
