#!/usr/bin/env python3
"""
Data Extraction Script for Development Database

This script extracts product and photo data from the development database
and provides utilities for analyzing and preparing data for migration.
"""

import sys
import os
import json
import argparse
from typing import List, Dict, Optional
from pathlib import Path

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'app'))

from database.dev_database import dev_db_manager, ProductWithPhotos, ProductData, ProductPhotoData
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DataExtractor:
    """
    Main class for extracting and analyzing data from the development database
    """
    
    def __init__(self):
        self.dev_db = dev_db_manager
        
    def test_connections(self) -> bool:
        """
        Test all database connections
        
        Returns:
            bool: True if all connections successful
        """
        logger.info("Testing database connections...")
        
        # Test development database
        if not self.dev_db.test_connection():
            logger.error("Failed to connect to development database")
            return False
            
        logger.info("✅ All database connections successful")
        return True
    
    def analyze_database(self) -> Dict:
        """
        Analyze the development database and return comprehensive statistics
        
        Returns:
            Dict: Database analysis results
        """
        logger.info("Analyzing development database...")
        
        stats = self.dev_db.get_database_stats()
        
        # Additional analysis
        analysis = {
            "database_stats": stats,
            "migration_readiness": {},
            "recommendations": []
        }
        
        # Calculate migration readiness
        total_products = stats.get('total_products', 0)
        products_with_s3 = stats.get('products_with_s3_photos', 0)
        products_with_desc = stats.get('products_with_descriptions', 0)
        
        if total_products > 0:
            s3_coverage = (products_with_s3 / total_products) * 100
            desc_coverage = (products_with_desc / total_products) * 100
            
            analysis["migration_readiness"] = {
                "s3_photo_coverage_percent": round(s3_coverage, 2),
                "description_coverage_percent": round(desc_coverage, 2),
                "products_ready_for_migration": products_with_s3
            }
            
            # Add recommendations
            if s3_coverage < 50:
                analysis["recommendations"].append(
                    f"Low S3 photo coverage ({s3_coverage:.1f}%). Consider focusing on products with S3 images."
                )
            
            if desc_coverage < 30:
                analysis["recommendations"].append(
                    f"Low description coverage ({desc_coverage:.1f}%). LLM enhancement will be crucial."
                )
            
            if products_with_s3 > 1000:
                analysis["recommendations"].append(
                    "Large dataset detected. Consider batch processing with progress tracking."
                )
        
        logger.info(f"Database analysis complete: {analysis}")
        return analysis
    
    def extract_sample_data(self, sample_size: int = 10) -> List[ProductWithPhotos]:
        """
        Extract a sample of products for testing and validation
        
        Args:
            sample_size: Number of products to extract
            
        Returns:
            List[ProductWithPhotos]: Sample products with photos
        """
        logger.info(f"Extracting sample data ({sample_size} products)...")
        
        products = self.dev_db.extract_products_batch(offset=0, limit=sample_size)
        
        # Filter to only include products with S3 photos
        products_with_s3 = []
        for product in products:
            s3_photos = [photo for photo in product.photos if photo.url_s3]
            if s3_photos:
                # Update the product to only include S3 photos
                product.photos = s3_photos
                products_with_s3.append(product)
        
        logger.info(f"Extracted {len(products_with_s3)} products with S3 photos")
        return products_with_s3
    
    def extract_all_data(self, batch_size: int = 100) -> List[ProductWithPhotos]:
        """
        Extract all products with S3 photos from the development database
        
        Args:
            batch_size: Size of each batch for processing
            
        Returns:
            List[ProductWithPhotos]: All products with S3 photos
        """
        logger.info("Extracting all products with S3 photos...")
        
        all_products = []
        offset = 0
        
        while True:
            batch = self.dev_db.extract_products_batch(offset=offset, limit=batch_size)
            
            if not batch:
                break
            
            # Filter to only include products with S3 photos
            for product in batch:
                s3_photos = [photo for photo in product.photos if photo.url_s3]
                if s3_photos:
                    product.photos = s3_photos
                    all_products.append(product)
            
            logger.info(f"Processed batch at offset {offset}, total products with S3 photos: {len(all_products)}")
            offset += batch_size
        
        logger.info(f"Extraction complete: {len(all_products)} products with S3 photos")
        return all_products
    
    def save_data_to_json(self, products: List[ProductWithPhotos], filename: str):
        """
        Save extracted data to JSON file for analysis or backup
        
        Args:
            products: List of products to save
            filename: Output filename
        """
        logger.info(f"Saving {len(products)} products to {filename}...")
        
        # Convert to serializable format
        data = []
        for product in products:
            product_data = {
                "product": {
                    "id": product.product.id,
                    "name": product.product.name,
                    "description": product.product.description,
                    "sale_price": product.product.sale_price,
                    "sku": product.product.sku,
                    "dropi_app_description": product.product.dropi_app_description
                },
                "photos": [
                    {
                        "id": photo.id,
                        "url": photo.url,
                        "product_id": photo.product_id,
                        "url_s3": photo.url_s3
                    }
                    for photo in product.photos
                ]
            }
            data.append(product_data)
        
        # Ensure output directory exists
        output_dir = Path(filename).parent
        output_dir.mkdir(parents=True, exist_ok=True)
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"✅ Data saved to {filename}")
    
    def validate_s3_urls(self, products: List[ProductWithPhotos]) -> Dict[str, int]:
        """
        Validate S3 URLs in the extracted data
        
        Args:
            products: List of products to validate
            
        Returns:
            Dict[str, int]: Validation statistics
        """
        logger.info("Validating S3 URLs...")
        
        stats = {
            "total_products": len(products),
            "total_photos": 0,
            "valid_s3_urls": 0,
            "invalid_s3_urls": 0,
            "empty_s3_urls": 0
        }
        
        for product in products:
            for photo in product.photos:
                stats["total_photos"] += 1
                
                if not photo.url_s3:
                    stats["empty_s3_urls"] += 1
                elif photo.url_s3.startswith('https://') and 's3' in photo.url_s3.lower():
                    stats["valid_s3_urls"] += 1
                else:
                    stats["invalid_s3_urls"] += 1
        
        logger.info(f"S3 URL validation: {stats}")
        return stats

def main():
    """Main function for command-line usage"""
    parser = argparse.ArgumentParser(description="Extract data from development database")
    parser.add_argument("--action", choices=["test", "analyze", "sample", "extract", "validate"], 
                       default="analyze", help="Action to perform")
    parser.add_argument("--sample-size", type=int, default=10, 
                       help="Sample size for sample extraction")
    parser.add_argument("--batch-size", type=int, default=100, 
                       help="Batch size for full extraction")
    parser.add_argument("--output", type=str, default="output/extracted_data.json", 
                       help="Output file for extracted data")
    
    args = parser.parse_args()
    
    extractor = DataExtractor()
    
    if args.action == "test":
        success = extractor.test_connections()
        sys.exit(0 if success else 1)
    
    elif args.action == "analyze":
        analysis = extractor.analyze_database()
        print(json.dumps(analysis, indent=2))
    
    elif args.action == "sample":
        products = extractor.extract_sample_data(args.sample_size)
        extractor.save_data_to_json(products, args.output)
        validation = extractor.validate_s3_urls(products)
        print(f"Sample extraction complete. Validation: {validation}")
    
    elif args.action == "extract":
        products = extractor.extract_all_data(args.batch_size)
        extractor.save_data_to_json(products, args.output)
        validation = extractor.validate_s3_urls(products)
        print(f"Full extraction complete. Validation: {validation}")
    
    elif args.action == "validate":
        # Load data from file and validate
        if os.path.exists(args.output):
            with open(args.output, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Convert back to ProductWithPhotos objects for validation
            products = []
            for item in data:
                product = ProductWithPhotos(
                    product=ProductData(**item["product"]),
                    photos=[ProductPhotoData(**photo) for photo in item["photos"]]
                )
                products.append(product)
            
            validation = extractor.validate_s3_urls(products)
            print(f"Validation results: {validation}")
        else:
            print(f"File {args.output} not found. Run extraction first.")

if __name__ == "__main__":
    main()
