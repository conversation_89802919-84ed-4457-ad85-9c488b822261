#!/usr/bin/env python3
"""
Verify Mexico Data Script

Script para verificar los productos migrados en la tabla de México.
"""

import sys
import os
from sqlalchemy import create_engine, text
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def verify_mexico_data():
    """
    Verificar los datos migrados en la tabla imagenes_mexico
    """
    print("🇲🇽 VERIFICACIÓN DE DATOS EN TABLA DE MÉXICO")
    print("=" * 60)
    
    # Get database URL from environment
    database_url = os.getenv("DATABASE_URL")
    engine = create_engine(database_url)
    
    try:
        with engine.connect() as conn:
            # Check if mexico table exists
            result = conn.execute(text("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = 'imagenes_mexico'
                )
            """))
            
            table_exists = result.scalar()
            
            if not table_exists:
                print("❌ La tabla 'imagenes_mexico' no existe")
                return
            
            print("✅ La tabla 'imagenes_mexico' existe")
            
            # Get total count
            result = conn.execute(text('SELECT COUNT(*) FROM imagenes_mexico'))
            total_count = result.scalar()
            print(f"📊 Total de registros en México: {total_count}")
            
            if total_count == 0:
                print("❌ No hay datos en la tabla de México")
                return
            
            # Show all migrated products
            print(f"\n🛍️ PRODUCTOS MIGRADOS A MÉXICO:")
            result = conn.execute(text("""
                SELECT idproducto, idphoto, 
                       LEFT(urlphoto, 80) as url_corta,
                       LEFT(texto, 100) as descripcion_corta,
                       empresa
                FROM imagenes_mexico 
                ORDER BY idproducto, idphoto
            """))
            
            current_product = None
            product_count = 0
            for row in result:
                if current_product != row.idproducto:
                    current_product = row.idproducto
                    product_count += 1
                    print(f"\n--- PRODUCTO {row.idproducto} ---")
                
                print(f"  📸 Foto {row.idphoto}")
                print(f"     URL: {row.url_corta}...")
                print(f"     Descripción: {row.descripcion_corta}...")
                print(f"     Empresa: {row.empresa}")
            
            print(f"\n📈 RESUMEN:")
            print(f"   Total productos únicos: {product_count}")
            print(f"   Total fotos: {total_count}")
            
            # Check for our specific migrated products
            migrated_ids = [1, 2, 3, 4, 5, 6, 7, 9]
            print(f"\n🎯 VERIFICACIÓN DE PRODUCTOS ESPECÍFICOS:")
            
            result = conn.execute(text("""
                SELECT idproducto, COUNT(*) as foto_count
                FROM imagenes_mexico 
                WHERE idproducto IN (1, 2, 3, 4, 5, 6, 7, 9)
                GROUP BY idproducto
                ORDER BY idproducto
            """))
            
            found_products = []
            for row in result:
                found_products.append(row.idproducto)
                print(f"   ✅ Producto {row.idproducto}: {row.foto_count} fotos")
            
            missing_products = set(migrated_ids) - set(found_products)
            if missing_products:
                print(f"   ❌ Productos faltantes: {list(missing_products)}")
            else:
                print(f"   🎉 Todos los productos esperados están en México!")
            
            # Show embedding status
            result = conn.execute(text("""
                SELECT 
                    COUNT(*) as total,
                    COUNT(embedding) as con_embedding,
                    COUNT(*) - COUNT(embedding) as sin_embedding
                FROM imagenes_mexico
            """))
            
            row = result.fetchone()
            print(f"\n🧠 Estado de embeddings en México:")
            print(f"   Total registros: {row.total}")
            print(f"   Con embedding: {row.con_embedding}")
            print(f"   Sin embedding: {row.sin_embedding}")
            
            if row.con_embedding > 0:
                print("   ✅ La tabla está lista para búsqueda semántica")
            else:
                print("   ❌ No hay embeddings disponibles")
            
            # Show all tables for comparison
            print(f"\n📋 TODAS LAS TABLAS DE BÚSQUEDA SEMÁNTICA:")
            result = conn.execute(text("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name LIKE 'imagenes_%'
                ORDER BY table_name
            """))
            
            for table in result:
                # Get count for each table
                count_result = conn.execute(text(f'SELECT COUNT(*) FROM {table.table_name}'))
                count = count_result.scalar()
                print(f"   - {table.table_name}: {count} registros")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    verify_mexico_data()
