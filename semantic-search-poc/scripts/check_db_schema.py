#!/usr/bin/env python3
"""
Database Schema Checker

Script para verificar la estructura de las tablas en la base de datos de desarrollo
"""

import sys
import os

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'app'))

from database.dev_database import dev_db_manager
from sqlalchemy import text

def check_table_schema():
    """
    Verificar la estructura de las tablas products y product_photos
    """
    print("🔍 Verificando estructura de la base de datos...")
    
    if not dev_db_manager.test_connection():
        print("❌ No se puede conectar a la base de datos")
        return
    
    try:
        with dev_db_manager.dev_engine.connect() as conn:
            # Verificar estructura de la tabla products
            print("\n📋 Estructura de la tabla 'products':")
            result = conn.execute(text("""
                SELECT column_name, data_type, is_nullable
                FROM information_schema.columns 
                WHERE table_name = 'products' 
                AND table_schema = 'public'
                ORDER BY ordinal_position
            """))
            
            for row in result:
                print(f"  - {row.column_name}: {row.data_type} ({'NULL' if row.is_nullable == 'YES' else 'NOT NULL'})")
            
            # Verificar estructura de la tabla product_photos
            print("\n📸 Estructura de la tabla 'product_photos':")
            result = conn.execute(text("""
                SELECT column_name, data_type, is_nullable
                FROM information_schema.columns 
                WHERE table_name = 'product_photos' 
                AND table_schema = 'public'
                ORDER BY ordinal_position
            """))
            
            for row in result:
                print(f"  - {row.column_name}: {row.data_type} ({'NULL' if row.is_nullable == 'YES' else 'NOT NULL'})")
            
            # Contar productos y fotos
            print("\n📊 Estadísticas de datos:")
            
            result = conn.execute(text("SELECT COUNT(*) FROM products"))
            products_count = result.scalar()
            print(f"  - Total productos: {products_count}")
            
            result = conn.execute(text("SELECT COUNT(*) FROM product_photos"))
            photos_count = result.scalar()
            print(f"  - Total fotos: {photos_count}")
            
            # Verificar productos con fotos
            result = conn.execute(text("""
                SELECT COUNT(DISTINCT p.id) 
                FROM products p 
                INNER JOIN product_photos pp ON p.id = pp.product_id
            """))
            products_with_photos = result.scalar()
            print(f"  - Productos con fotos: {products_with_photos}")
            
            # Mostrar algunos ejemplos de datos
            print("\n🔍 Muestra de datos de product_photos (primeras 5 filas):")
            result = conn.execute(text("""
                SELECT id, product_id, url, 
                       CASE 
                           WHEN LENGTH(url) > 50 THEN CONCAT(LEFT(url, 47), '...')
                           ELSE url
                       END as url_short
                FROM product_photos 
                LIMIT 5
            """))
            
            for row in result:
                print(f"  - ID: {row.id}, Product: {row.product_id}, URL: {row.url_short}")
            
            # Verificar si existe alguna columna relacionada con S3
            print("\n🔍 Columnas que contienen 'url' en product_photos:")
            result = conn.execute(text("""
                SELECT column_name, data_type
                FROM information_schema.columns 
                WHERE table_name = 'product_photos' 
                AND table_schema = 'public'
                AND LOWER(column_name) LIKE '%url%'
                ORDER BY column_name
            """))
            
            for row in result:
                print(f"  - {row.column_name}: {row.data_type}")
                
    except Exception as e:
        print(f"❌ Error verificando esquema: {e}")

if __name__ == "__main__":
    check_table_schema()
