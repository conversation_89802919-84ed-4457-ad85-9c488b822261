#!/usr/bin/env python3
"""
Migration Execution Script

Standalone script to execute the complete data migration process from development
database to semantic search database with comprehensive options and logging.
"""

import sys
import os
import asyncio
import argparse
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Optional

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'app'))

from database.dev_database import dev_db_manager, ProductWithPhotos
from services.migration_service import migration_service, MigrationResult, MigrationStats
from utils.validation import data_validator

# Configure logging
def setup_logging(log_level: str = "INFO", log_file: Optional[str] = None):
    """Setup logging configuration"""
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # Configure root logger
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format=log_format,
        handlers=[]
    )
    
    # Add console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(logging.Formatter(log_format))
    logging.getLogger().addHandler(console_handler)
    
    # Add file handler if specified
    if log_file:
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(logging.Formatter(log_format))
        logging.getLogger().addHandler(file_handler)

logger = logging.getLogger(__name__)

class MigrationExecutor:
    """
    Main executor for the migration process
    """
    
    def __init__(self, company_name: str = "dropi_colombia"):
        self.company_name = company_name
        self.migration_service = migration_service
        self.migration_service.company_name = company_name
        
    async def run_full_migration(self, 
                               batch_size: int = 50,
                               sample_size: Optional[int] = None,
                               validate_only: bool = False,
                               resume_from: Optional[str] = None) -> Dict:
        """
        Run the complete migration process
        
        Args:
            batch_size: Size of processing batches
            sample_size: Limit to sample size for testing
            validate_only: Only validate data, don't migrate
            resume_from: Resume from a specific checkpoint file
            
        Returns:
            Dictionary with migration results
        """
        logger.info(f"Starting migration for company: {self.company_name}")
        logger.info(f"Batch size: {batch_size}, Sample size: {sample_size}")
        
        # Step 1: Test connections
        logger.info("Testing database connections...")
        if not dev_db_manager.test_connection():
            raise Exception("Failed to connect to development database")
        
        if not self.migration_service.target_db.test_connection():
            raise Exception("Failed to connect to target database")
        
        logger.info("✅ Database connections successful")
        
        # Step 2: Analyze development database
        logger.info("Analyzing development database...")
        stats = dev_db_manager.get_database_stats()
        logger.info(f"Database stats: {stats}")
        
        # Step 3: Extract products
        logger.info("Extracting products from development database...")
        
        if resume_from and os.path.exists(resume_from):
            logger.info(f"Resuming from checkpoint: {resume_from}")
            with open(resume_from, 'r', encoding='utf-8') as f:
                checkpoint_data = json.load(f)
            
            # Convert back to ProductWithPhotos objects
            products = []
            for item in checkpoint_data.get("products", []):
                from database.dev_database import ProductData, ProductPhotoData
                product = ProductWithPhotos(
                    product=ProductData(**item["product"]),
                    photos=[ProductPhotoData(**photo) for photo in item["photos"]]
                )
                products.append(product)
            
            logger.info(f"Loaded {len(products)} products from checkpoint")
        else:
            if sample_size:
                products = dev_db_manager.extract_products_batch(offset=0, limit=sample_size)
            else:
                # Extract all products in batches
                all_products = []
                offset = 0
                
                while True:
                    batch = dev_db_manager.extract_products_batch(offset=offset, limit=batch_size)
                    if not batch:
                        break
                    
                    all_products.extend(batch)
                    logger.info(f"Extracted batch at offset {offset}, total: {len(all_products)}")
                    offset += batch_size
                
                products = all_products
            
            # Filter to only products with S3 photos
            products = [p for p in products if any(photo.url_s3 for photo in p.photos)]
            logger.info(f"Found {len(products)} products with S3 photos")
        
        if not products:
            raise Exception("No products with S3 photos found for migration")
        
        # Step 4: Validate data
        logger.info("Validating extracted data...")
        validation_results = self.migration_service.validate_migration_batch(products)
        
        logger.info(f"Validation results:")
        logger.info(f"  Total products: {validation_results['total_products']}")
        logger.info(f"  Valid products: {validation_results['valid_products']}")
        logger.info(f"  Invalid products: {validation_results['invalid_products']}")
        logger.info(f"  Migration ready: {validation_results['migration_ready_products']}")
        logger.info(f"  Total errors: {validation_results['total_errors']}")
        logger.info(f"  Total warnings: {validation_results['total_warnings']}")
        
        if validation_results['total_errors'] > 0:
            logger.warning("Validation errors found:")
            for error in validation_results['errors'][:10]:  # Show first 10 errors
                logger.warning(f"  - {error}")
            if len(validation_results['errors']) > 10:
                logger.warning(f"  ... and {len(validation_results['errors']) - 10} more errors")
        
        if validate_only:
            logger.info("Validation-only mode: stopping here")
            return {
                "validation_results": validation_results,
                "migration_completed": False
            }
        
        # Filter to only valid products for migration
        valid_products = []
        for i, product in enumerate(products):
            product_result = validation_results['product_results'][i]
            if product_result['is_valid']:
                valid_products.append(product)
        
        logger.info(f"Proceeding with migration of {len(valid_products)} valid products")
        
        # Step 5: Ensure target table exists
        logger.info("Ensuring target company table exists...")
        if not self.migration_service.ensure_company_table_exists():
            raise Exception("Failed to create/verify target company table")
        
        # Step 6: Run migration
        logger.info("Starting product migration...")
        
        all_results = []
        all_stats = MigrationStats(total_products=len(valid_products), start_time=datetime.now())
        
        # Process in batches
        for i in range(0, len(valid_products), batch_size):
            batch = valid_products[i:i + batch_size]
            batch_num = (i // batch_size) + 1
            total_batches = (len(valid_products) + batch_size - 1) // batch_size
            
            logger.info(f"Processing batch {batch_num}/{total_batches} ({len(batch)} products)")
            
            try:
                batch_results, batch_stats = await self.migration_service.migrate_products_batch(batch)
                all_results.extend(batch_results)
                
                # Update cumulative stats
                all_stats.successful_products += batch_stats.successful_products
                all_stats.failed_products += batch_stats.failed_products
                all_stats.total_photos_processed += batch_stats.total_photos_processed
                all_stats.total_processing_time += batch_stats.total_processing_time
                all_stats.errors.extend(batch_stats.errors)
                
                logger.info(f"Batch {batch_num} complete: {batch_stats.successful_products}/{len(batch)} successful")
                
                # Save checkpoint after each batch
                checkpoint_file = f"output/checkpoints/migration_checkpoint_{self.company_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                self.save_checkpoint(valid_products[i+len(batch):], checkpoint_file)
                
            except Exception as e:
                logger.error(f"Batch {batch_num} failed: {e}")
                all_stats.errors.append(f"Batch {batch_num}: {str(e)}")
                # Continue with next batch
        
        all_stats.end_time = datetime.now()
        
        # Step 7: Save final report
        report_filename = f"output/migration_reports/migration_{self.company_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        self.migration_service.save_migration_report(all_results, all_stats, report_filename)
        
        logger.info("Migration completed!")
        logger.info(f"Final results: {all_stats.successful_products}/{all_stats.total_products} successful")
        logger.info(f"Total photos processed: {all_stats.total_photos_processed}")
        logger.info(f"Total processing time: {all_stats.total_processing_time:.2f} seconds")
        logger.info(f"Report saved to: {report_filename}")
        
        return {
            "validation_results": validation_results,
            "migration_completed": True,
            "migration_stats": all_stats,
            "report_file": report_filename,
            "total_products_processed": len(all_results),
            "successful_products": all_stats.successful_products,
            "failed_products": all_stats.failed_products
        }
    
    def save_checkpoint(self, remaining_products: List[ProductWithPhotos], filename: str):
        """Save checkpoint for resume functionality"""
        if not remaining_products:
            return
        
        # Convert to serializable format
        checkpoint_data = {
            "company_name": self.company_name,
            "timestamp": datetime.now().isoformat(),
            "remaining_products": len(remaining_products),
            "products": []
        }
        
        for product in remaining_products:
            product_data = {
                "product": {
                    "id": product.product.id,
                    "name": product.product.name,
                    "description": product.product.description,
                    "sale_price": product.product.sale_price,
                    "sku": product.product.sku,
                    "dropi_app_description": product.product.dropi_app_description
                },
                "photos": [
                    {
                        "id": photo.id,
                        "url": photo.url,
                        "product_id": photo.product_id,
                        "url_s3": photo.url_s3
                    }
                    for photo in product.photos
                ]
            }
            checkpoint_data["products"].append(product_data)
        
        # Ensure output directory exists
        output_dir = Path(filename).parent
        output_dir.mkdir(parents=True, exist_ok=True)
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(checkpoint_data, f, indent=2, ensure_ascii=False)
        
        logger.debug(f"Checkpoint saved: {filename}")

async def main():
    """Main function for command-line usage"""
    parser = argparse.ArgumentParser(description="Execute data migration from development to semantic search database")
    
    parser.add_argument("--company", default="dropi_colombia", help="Company name for migration")
    parser.add_argument("--batch-size", type=int, default=50, help="Batch size for processing")
    parser.add_argument("--sample-size", type=int, help="Limit migration to sample size for testing")
    parser.add_argument("--validate-only", action="store_true", help="Only validate data, don't migrate")
    parser.add_argument("--resume-from", type=str, help="Resume from checkpoint file")
    parser.add_argument("--log-level", default="INFO", choices=["DEBUG", "INFO", "WARNING", "ERROR"], help="Logging level")
    parser.add_argument("--log-file", type=str, help="Log file path")
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.log_level, args.log_file)
    
    try:
        executor = MigrationExecutor(args.company)
        
        results = await executor.run_full_migration(
            batch_size=args.batch_size,
            sample_size=args.sample_size,
            validate_only=args.validate_only,
            resume_from=args.resume_from
        )
        
        print("\n" + "="*80)
        print("MIGRATION SUMMARY")
        print("="*80)
        
        if results["migration_completed"]:
            print(f"✅ Migration completed successfully!")
            print(f"   Products processed: {results['total_products_processed']}")
            print(f"   Successful: {results['successful_products']}")
            print(f"   Failed: {results['failed_products']}")
            print(f"   Report: {results['report_file']}")
        else:
            print(f"ℹ️  Validation completed (migration not run)")
        
        validation = results["validation_results"]
        print(f"\nValidation Results:")
        print(f"   Total products: {validation['total_products']}")
        print(f"   Valid: {validation['valid_products']}")
        print(f"   Invalid: {validation['invalid_products']}")
        print(f"   Migration ready: {validation['migration_ready_products']}")
        
        if validation['total_errors'] > 0:
            print(f"   ⚠️  Errors: {validation['total_errors']}")
        if validation['total_warnings'] > 0:
            print(f"   ⚠️  Warnings: {validation['total_warnings']}")
        
        print("="*80)
        
    except Exception as e:
        logger.error(f"Migration failed: {e}")
        print(f"\n❌ Migration failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
