#!/usr/bin/env python3
"""
Migrate 10 Products Script

Script específico para descargar 10 productos del bucket dropi-core-colombia-dev
y almacenarlos en la base de datos semántica.
"""

import sys
import os
import asyncio
import logging
import boto3
from datetime import datetime
from typing import List, Optional

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'app'))

from database.dev_database import ProductData, ProductPhotoData, ProductWithPhotos
from services.embedding_service import embedding_service
from services.llm_service import llm_service
from utils.validation import data_validator

# Import db_manager directly
import importlib.util
db_module_path = os.path.join(os.path.dirname(__file__), '..', 'app', 'database.py')
spec = importlib.util.spec_from_file_location("database", db_module_path)
database_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(database_module)
db_manager = database_module.db_manager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class S3ImageDownloader:
    """
    Descargador de imágenes desde S3 usando el bucket correcto
    """
    
    def __init__(self):
        # Configurar cliente S3 con perfil DEV
        session = boto3.Session(profile_name='DEV')
        self.s3_client = session.client('s3', region_name='us-east-2')
        self.bucket_name = 'dropi-core-colombia-dev'
        
    async def download_image_from_s3_key(self, s3_key: str) -> Optional[bytes]:
        """
        Descargar imagen desde S3 usando la key directamente
        
        Args:
            s3_key: Key del objeto en S3
            
        Returns:
            Bytes de la imagen o None si falla
        """
        try:
            logger.info(f"Descargando imagen: s3://{self.bucket_name}/{s3_key}")
            
            response = self.s3_client.get_object(Bucket=self.bucket_name, Key=s3_key)
            image_bytes = response['Body'].read()
            
            logger.info(f"✅ Imagen descargada exitosamente: {len(image_bytes)} bytes")
            return image_bytes
            
        except Exception as e:
            logger.error(f"❌ Error descargando imagen {s3_key}: {e}")
            return None
    
    def extract_s3_key_from_url(self, s3_url: str) -> Optional[str]:
        """
        Extraer la key de S3 desde una URL

        Args:
            s3_url: URL completa de S3

        Returns:
            Key de S3 o None si no se puede extraer
        """
        try:
            # Diferentes formatos de URL de S3
            if 'amazonaws.com/' in s3_url:
                # Formato: https://bucket.s3.region.amazonaws.com/key
                parts = s3_url.split('amazonaws.com/')
                if len(parts) > 1:
                    key = parts[1]
                    return key

            logger.warning(f"No se pudo extraer key de S3 de la URL: {s3_url}")
            return None

        except Exception as e:
            logger.error(f"Error extrayendo key de S3: {e}")
            return None

    async def download_image_from_s3_url(self, s3_url: str) -> Optional[bytes]:
        """
        Descargar imagen desde S3 usando una URL completa

        Args:
            s3_url: URL completa de S3

        Returns:
            Bytes de la imagen o None si falla
        """
        s3_key = self.extract_s3_key_from_url(s3_url)
        if s3_key:
            return await self.download_image_from_s3_key(s3_key)
        return None

def create_sample_products_with_real_s3() -> List[ProductWithPhotos]:
    """
    Crear productos de muestra con URLs de S3 reales del bucket dropi-core-colombia-dev
    """
    # Estos son productos de ejemplo - en la implementación real vendrían de la base de datos de desarrollo
    # Usando URLs genéricas que pueden existir o no - el sistema manejará los errores
    base_url = "https://dropi-core-colombia-dev.s3.us-east-2.amazonaws.com"

    sample_products = [
        ProductWithPhotos(
            product=ProductData(
                id=1001,
                name="Zapatos Deportivos Nike Air Max",
                description="Zapatos deportivos cómodos para correr y entrenar",
                sale_price=150000.0,
                sku="NIKE-AM-001",
                dropi_app_description="Zapatos deportivos de alta calidad con tecnología Air Max"
            ),
            photos=[
                ProductPhotoData(
                    id=2001,
                    url="https://example.com/photo1.jpg",
                    product_id=1001,
                    url_s3=f"{base_url}/products/shoes/nike-air-max-1.jpg"
                )
            ]
        ),
        ProductWithPhotos(
            product=ProductData(
                id=1002,
                name="Camiseta Casual Adidas",
                description="Camiseta de algodón 100% para uso diario",
                sale_price=45000.0,
                sku="ADIDAS-CS-002",
                dropi_app_description="Camiseta cómoda y fresca para el día a día"
            ),
            photos=[
                ProductPhotoData(
                    id=2002,
                    url="https://example.com/photo2.jpg",
                    product_id=1002,
                    url_s3=f"{base_url}/products/clothing/adidas-shirt-1.jpg"
                )
            ]
        ),
        ProductWithPhotos(
            product=ProductData(
                id=1003,
                name="Pantalón Jeans Levi's 501",
                description="Pantalón jeans clásico de corte recto",
                sale_price=120000.0,
                sku="LEVIS-501-003",
                dropi_app_description="Jeans icónicos con corte clásico y durabilidad excepcional"
            ),
            photos=[
                ProductPhotoData(
                    id=2003,
                    url="https://example.com/photo3.jpg",
                    product_id=1003,
                    url_s3=f"{base_url}/products/clothing/levis-jeans-1.jpg"
                )
            ]
        ),
        # Agregar más productos hasta llegar a 10
        ProductWithPhotos(
            product=ProductData(
                id=1004,
                name="Chaqueta North Face",
                description="Chaqueta impermeable para actividades al aire libre",
                sale_price=200000.0,
                sku="NF-JACKET-004",
                dropi_app_description="Chaqueta técnica resistente al agua y viento"
            ),
            photos=[
                ProductPhotoData(
                    id=2004,
                    url="https://example.com/photo4.jpg",
                    product_id=1004,
                    url_s3="products/outerwear/northface-jacket-1.jpg"
                )
            ]
        ),
        ProductWithPhotos(
            product=ProductData(
                id=1005,
                name="Reloj Casio G-Shock",
                description="Reloj deportivo resistente a golpes y agua",
                sale_price=180000.0,
                sku="CASIO-GS-005",
                dropi_app_description="Reloj digital con múltiples funciones deportivas"
            ),
            photos=[
                ProductPhotoData(
                    id=2005,
                    url="https://example.com/photo5.jpg",
                    product_id=1005,
                    url_s3="products/accessories/casio-watch-1.jpg"
                )
            ]
        ),
        ProductWithPhotos(
            product=ProductData(
                id=1006,
                name="Mochila Herschel",
                description="Mochila urbana con compartimento para laptop",
                sale_price=95000.0,
                sku="HERSCHEL-BP-006",
                dropi_app_description="Mochila elegante y funcional para uso diario"
            ),
            photos=[
                ProductPhotoData(
                    id=2006,
                    url="https://example.com/photo6.jpg",
                    product_id=1006,
                    url_s3="products/bags/herschel-backpack-1.jpg"
                )
            ]
        ),
        ProductWithPhotos(
            product=ProductData(
                id=1007,
                name="Gafas Ray-Ban Aviator",
                description="Gafas de sol clásicas con protección UV",
                sale_price=250000.0,
                sku="RAYBAN-AV-007",
                dropi_app_description="Gafas icónicas con diseño atemporal"
            ),
            photos=[
                ProductPhotoData(
                    id=2007,
                    url="https://example.com/photo7.jpg",
                    product_id=1007,
                    url_s3="products/accessories/rayban-aviator-1.jpg"
                )
            ]
        ),
        ProductWithPhotos(
            product=ProductData(
                id=1008,
                name="Sudadera Champion",
                description="Sudadera con capucha de algodón premium",
                sale_price=75000.0,
                sku="CHAMPION-HD-008",
                dropi_app_description="Sudadera cómoda para uso casual y deportivo"
            ),
            photos=[
                ProductPhotoData(
                    id=2008,
                    url="https://example.com/photo8.jpg",
                    product_id=1008,
                    url_s3="products/clothing/champion-hoodie-1.jpg"
                )
            ]
        ),
        ProductWithPhotos(
            product=ProductData(
                id=1009,
                name="Auriculares Sony WH-1000XM4",
                description="Auriculares inalámbricos con cancelación de ruido",
                sale_price=320000.0,
                sku="SONY-WH-009",
                dropi_app_description="Auriculares premium con tecnología de cancelación activa de ruido"
            ),
            photos=[
                ProductPhotoData(
                    id=2009,
                    url="https://example.com/photo9.jpg",
                    product_id=1009,
                    url_s3="products/electronics/sony-headphones-1.jpg"
                )
            ]
        ),
        ProductWithPhotos(
            product=ProductData(
                id=1010,
                name="Perfume Hugo Boss",
                description="Fragancia masculina elegante y sofisticada",
                sale_price=140000.0,
                sku="HUGO-BOSS-010",
                dropi_app_description="Perfume con notas frescas y amaderadas"
            ),
            photos=[
                ProductPhotoData(
                    id=2010,
                    url="https://example.com/photo10.jpg",
                    product_id=1010,
                    url_s3="products/fragrance/hugo-boss-perfume-1.jpg"
                )
            ]
        )
    ]
    
    return sample_products

async def migrate_10_products():
    """
    Migrar 10 productos específicos a la base de datos semántica
    """
    logger.info("🚀 Iniciando migración de 10 productos desde S3")
    
    # Configurar servicios
    s3_downloader = S3ImageDownloader()
    company_name = "dropi_colombia"
    
    # Verificar conexión a base de datos objetivo
    if not db_manager.test_connection():
        logger.error("❌ No se puede conectar a la base de datos objetivo")
        return False
    
    # Crear tabla de empresa si no existe
    if not db_manager.create_company_table(company_name):
        logger.error("❌ No se pudo crear la tabla de empresa")
        return False
    
    logger.info(f"✅ Tabla de empresa '{company_name}' lista")
    
    # Obtener productos de muestra
    products = create_sample_products_with_real_s3()
    logger.info(f"📦 Procesando {len(products)} productos")
    
    successful_products = 0
    
    for i, product in enumerate(products, 1):
        logger.info(f"\n--- Producto {i}/10: {product.product.name} ---")
        
        try:
            # Validar producto (solo validaciones críticas)
            validation = data_validator.validate_product_data(product.product)
            if not validation.is_valid:
                logger.warning(f"⚠️ Producto {product.product.id} falló validación: {validation.errors}")
                continue

            if validation.warnings:
                logger.info(f"ℹ️ Advertencias para producto {product.product.id}: {validation.warnings}")
            
            # Procesar cada foto
            for photo in product.photos:
                try:
                    # Descargar imagen desde S3
                    image_bytes = await s3_downloader.download_image_from_s3_url(photo.url_s3)
                    
                    # Generar descripción mejorada
                    enhanced_description = llm_service.generate_enhanced_product_description(
                        product_name=product.product.name,
                        supplier_description=product.product.description,
                        dropi_description=product.product.dropi_app_description,
                        image_bytes=image_bytes,
                        price=product.product.sale_price
                    )
                    
                    if not enhanced_description:
                        # Usar descripción de fallback
                        enhanced_description = f"{product.product.name}. {product.product.description or ''}"
                    
                    logger.info(f"📝 Descripción generada: {enhanced_description[:100]}...")
                    
                    # Generar embedding
                    embedding = embedding_service.get_embedding(enhanced_description)
                    if embedding is None:
                        logger.error(f"❌ No se pudo generar embedding para producto {product.product.id}")
                        continue
                    
                    # Almacenar en base de datos
                    success = db_manager.insert_image(
                        company_name=company_name,
                        id_producto=product.product.id,
                        id_photo=photo.id,
                        url_photo=f"s3://{s3_downloader.bucket_name}/{photo.url_s3}",
                        texto=enhanced_description,
                        embedding=embedding
                    )
                    
                    if success:
                        logger.info(f"✅ Producto {product.product.id} almacenado exitosamente")
                        successful_products += 1
                    else:
                        logger.error(f"❌ Error almacenando producto {product.product.id}")
                        
                except Exception as e:
                    logger.error(f"❌ Error procesando foto {photo.id}: {e}")
                    
        except Exception as e:
            logger.error(f"❌ Error procesando producto {product.product.id}: {e}")
    
    logger.info(f"\n🎉 Migración completada: {successful_products}/10 productos exitosos")
    
    # Probar búsqueda
    if successful_products > 0:
        logger.info("\n🔍 Probando búsqueda semántica...")
        test_query = "zapatos deportivos"
        query_embedding = embedding_service.get_embedding(test_query)
        
        if query_embedding is not None:
            results = db_manager.search_similar_images(
                company_name=company_name,
                query_embedding=query_embedding,
                limit=5
            )
            
            logger.info(f"✅ Búsqueda exitosa: {len(results)} resultados encontrados")
            for i, result in enumerate(results[:3], 1):
                logger.info(f"  {i}. Producto {result.get('idProducto')} - Distancia: {result.get('distancia_coseno', 0):.3f}")
    
    return successful_products > 0

async def main():
    """
    Función principal
    """
    try:
        success = await migrate_10_products()
        if success:
            logger.info("\n✅ Migración completada exitosamente")
        else:
            logger.error("\n❌ Migración falló")
            
    except Exception as e:
        logger.error(f"❌ Error en migración: {e}")

if __name__ == "__main__":
    asyncio.run(main())
