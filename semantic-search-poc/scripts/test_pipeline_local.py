#!/usr/bin/env python3
"""
Local Pipeline Test Script

Test the migration pipeline with simulated data to verify all components work correctly
before attempting to connect to the real development database.
"""

import sys
import os
import asyncio
import logging
from datetime import datetime
from typing import List

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'app'))

from database.dev_database import ProductData, ProductPhotoData, ProductWithPhotos
from services.embedding_service import embedding_service
from services.llm_service import llm_service
from utils.validation import data_validator

# Import db_manager directly from the database module file
import importlib.util
import os
db_module_path = os.path.join(os.path.dirname(__file__), '..', 'app', 'database.py')
spec = importlib.util.spec_from_file_location("database", db_module_path)
database_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(database_module)
db_manager = database_module.db_manager

# Import migration service after db_manager is available
from services.migration_service import migration_service

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_test_products() -> List[ProductWithPhotos]:
    """
    Create test products with realistic data for pipeline testing
    """
    test_products = []
    
    # Product 1: Zapatos deportivos
    product1 = ProductWithPhotos(
        product=ProductData(
            id=1001,
            name="Zapatos Deportivos Nike Air Max",
            description="Zapatos deportivos cómodos para correr y entrenar",
            sale_price=150000.0,
            sku="NIKE-AM-001",
            dropi_app_description="Zapatos deportivos de alta calidad con tecnología Air Max"
        ),
        photos=[
            ProductPhotoData(
                id=2001,
                url="https://example.com/photo1.jpg",
                product_id=1001,
                url_s3="https://dropi-images.s3.us-east-2.amazonaws.com/products/zapatos-nike-1.jpg"
            ),
            ProductPhotoData(
                id=2002,
                url="https://example.com/photo2.jpg", 
                product_id=1001,
                url_s3="https://dropi-images.s3.us-east-2.amazonaws.com/products/zapatos-nike-2.jpg"
            )
        ]
    )
    
    # Product 2: Camiseta casual
    product2 = ProductWithPhotos(
        product=ProductData(
            id=1002,
            name="Camiseta Casual Adidas",
            description="Camiseta de algodón 100% para uso diario",
            sale_price=45000.0,
            sku="ADIDAS-CS-002",
            dropi_app_description="Camiseta cómoda y fresca para el día a día"
        ),
        photos=[
            ProductPhotoData(
                id=2003,
                url="https://example.com/photo3.jpg",
                product_id=1002,
                url_s3="https://dropi-images.s3.us-east-2.amazonaws.com/products/camiseta-adidas-1.jpg"
            )
        ]
    )
    
    # Product 3: Pantalón jeans
    product3 = ProductWithPhotos(
        product=ProductData(
            id=1003,
            name="Pantalón Jeans Levi's 501",
            description="Pantalón jeans clásico de corte recto",
            sale_price=120000.0,
            sku="LEVIS-501-003",
            dropi_app_description="Jeans icónicos con corte clásico y durabilidad excepcional"
        ),
        photos=[
            ProductPhotoData(
                id=2004,
                url="https://example.com/photo4.jpg",
                product_id=1003,
                url_s3="https://dropi-images.s3.us-east-2.amazonaws.com/products/jeans-levis-1.jpg"
            )
        ]
    )
    
    test_products.extend([product1, product2, product3])
    return test_products

async def test_pipeline_components():
    """
    Test individual pipeline components
    """
    logger.info("🧪 Testing pipeline components...")
    
    # Test 1: Embedding service
    logger.info("Testing embedding service...")
    try:
        test_text = "Zapatos deportivos Nike Air Max para correr"
        embedding = embedding_service.get_embedding(test_text)
        if embedding is not None:
            logger.info(f"✅ Embedding service working (dimensions: {embedding.shape})")
        else:
            logger.error("❌ Embedding service failed")
            return False
    except Exception as e:
        logger.error(f"❌ Embedding service error: {e}")
        return False
    
    # Test 2: LLM service
    logger.info("Testing LLM service...")
    try:
        enhanced_desc = llm_service.generate_enhanced_product_description(
            product_name="Zapatos Nike Air Max",
            supplier_description="Zapatos deportivos cómodos",
            price=150000.0
        )
        if enhanced_desc:
            logger.info(f"✅ LLM service working")
            logger.info(f"   Generated description: {enhanced_desc[:100]}...")
        else:
            logger.warning("⚠️ LLM service returned empty description")
    except Exception as e:
        logger.error(f"❌ LLM service error: {e}")
        return False
    
    # Test 3: Target database connection
    logger.info("Testing target database connection...")
    try:
        if db_manager.test_connection():
            logger.info("✅ Target database connection working")
        else:
            logger.error("❌ Target database connection failed")
            return False
    except Exception as e:
        logger.error(f"❌ Target database error: {e}")
        return False
    
    # Test 4: Validation system
    logger.info("Testing validation system...")
    try:
        test_products = create_test_products()
        validation_results = data_validator.validate_batch(test_products)
        
        logger.info(f"✅ Validation system working")
        logger.info(f"   Valid products: {validation_results['valid_products']}/{validation_results['total_products']}")
        logger.info(f"   Errors: {validation_results['total_errors']}")
        logger.info(f"   Warnings: {validation_results['total_warnings']}")
        
        if validation_results['total_errors'] > 0:
            logger.warning("   Some validation errors found:")
            for error in validation_results['errors'][:3]:
                logger.warning(f"     - {error}")
    except Exception as e:
        logger.error(f"❌ Validation system error: {e}")
        return False
    
    logger.info("🎉 All pipeline components tested successfully!")
    return True

async def test_end_to_end_migration():
    """
    Test end-to-end migration with simulated data
    """
    logger.info("🚀 Testing end-to-end migration pipeline...")
    
    # Create test products
    test_products = create_test_products()
    logger.info(f"Created {len(test_products)} test products")
    
    # Ensure company table exists
    company_name = "test_company"
    migration_service.company_name = company_name
    
    if not migration_service.ensure_company_table_exists():
        logger.error("❌ Failed to create company table")
        return False
    
    logger.info(f"✅ Company table '{company_name}' ready")
    
    # Run migration
    try:
        results, stats = await migration_service.migrate_products_batch(test_products)
        
        logger.info("📊 Migration Results:")
        logger.info(f"   Total products: {stats.total_products}")
        logger.info(f"   Successful: {stats.successful_products}")
        logger.info(f"   Failed: {stats.failed_products}")
        logger.info(f"   Photos processed: {stats.total_photos_processed}")
        logger.info(f"   Processing time: {stats.total_processing_time:.2f}s")
        
        if stats.errors:
            logger.warning("   Errors encountered:")
            for error in stats.errors[:3]:
                logger.warning(f"     - {error}")
        
        # Test search functionality
        if stats.successful_products > 0:
            logger.info("🔍 Testing search functionality...")
            
            # Generate embedding for search query
            search_query = "zapatos deportivos Nike"
            query_embedding = embedding_service.get_embedding(search_query)
            
            if query_embedding is not None:
                # Search for similar products
                search_results = db_manager.search_similar_images(
                    company_name=company_name,
                    query_embedding=query_embedding,
                    limit=5
                )
                
                logger.info(f"✅ Search test successful: found {len(search_results)} results")
                for i, result in enumerate(search_results[:2]):
                    logger.info(f"   Result {i+1}: Product {result.get('idProducto')} (distance: {result.get('distancia_coseno', 'N/A'):.3f})")
            else:
                logger.error("❌ Failed to generate search embedding")
        
        return stats.successful_products > 0
        
    except Exception as e:
        logger.error(f"❌ End-to-end migration failed: {e}")
        return False

async def main():
    """
    Main test function
    """
    logger.info("🎯 Starting Local Pipeline Test")
    logger.info("="*60)
    
    try:
        # Test individual components
        components_ok = await test_pipeline_components()
        if not components_ok:
            logger.error("❌ Component tests failed")
            return
        
        logger.info("\n" + "="*60)
        
        # Test end-to-end migration
        migration_ok = await test_end_to_end_migration()
        if migration_ok:
            logger.info("\n🎉 ALL TESTS PASSED!")
            logger.info("The migration pipeline is ready for real data.")
        else:
            logger.error("\n❌ End-to-end test failed")
            
    except Exception as e:
        logger.error(f"❌ Test execution failed: {e}")
    
    logger.info("="*60)
    logger.info("🏁 Local Pipeline Test Complete")

if __name__ == "__main__":
    asyncio.run(main())
