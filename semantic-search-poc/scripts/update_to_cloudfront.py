#!/usr/bin/env python3
"""
Update to CloudFront URLs Script

Script para actualizar las URLs existentes en la base de datos semántica
de S3 directo a CloudFront para mejorar el rendimiento de carga de imágenes.
"""

import sys
import os
import logging
from sqlalchemy import create_engine, text
from dotenv import load_dotenv

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'app'))

from utils.cloudfront import cloudfront_generator

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def update_table_to_cloudfront(table_name: str, dry_run: bool = True):
    """
    Actualizar URLs de una tabla específica a CloudFront
    
    Args:
        table_name: Nombre de la tabla a actualizar
        dry_run: Si True, solo muestra qué se actualizaría sin hacer cambios
    """
    print(f"🔄 {'[DRY RUN] ' if dry_run else ''}Actualizando tabla '{table_name}' a CloudFront...")
    
    # Get database URL from environment
    database_url = os.getenv("DATABASE_URL")
    engine = create_engine(database_url)
    
    try:
        with engine.connect() as conn:
            # Check if table exists
            result = conn.execute(text("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = :table_name
                )
            """), {"table_name": table_name})
            
            if not result.scalar():
                print(f"❌ La tabla '{table_name}' no existe")
                return False
            
            # Get all records with S3 URLs
            result = conn.execute(text(f"""
                SELECT idproducto, idphoto, urlphoto
                FROM {table_name}
                WHERE urlphoto IS NOT NULL 
                AND urlphoto != ''
                ORDER BY idproducto, idphoto
            """))
            
            records = result.fetchall()
            
            if not records:
                print(f"❌ No hay registros con URLs en la tabla '{table_name}'")
                return False
            
            print(f"📊 Encontrados {len(records)} registros para actualizar")
            
            updates_needed = 0
            cloudfront_updates = []
            
            for record in records:
                current_url = record.urlphoto
                
                # Check if it's already a CloudFront URL
                if cloudfront_generator.is_cloudfront_url(current_url):
                    continue
                
                # Convert to CloudFront URL
                cloudfront_url = cloudfront_generator.convert_s3_url_to_cloudfront(current_url)
                
                if cloudfront_url and cloudfront_url != current_url:
                    updates_needed += 1
                    cloudfront_updates.append({
                        'product_id': record.idproducto,
                        'photo_id': record.idphoto,
                        'old_url': current_url,
                        'new_url': cloudfront_url
                    })
                    
                    if dry_run:
                        print(f"  📸 Producto {record.idproducto}, Foto {record.idphoto}:")
                        print(f"     Actual: {current_url[:80]}...")
                        print(f"     Nuevo:  {cloudfront_url[:80]}...")
                        print()
            
            print(f"📈 Resumen:")
            print(f"   Total registros: {len(records)}")
            print(f"   Ya con CloudFront: {len(records) - updates_needed}")
            print(f"   Necesitan actualización: {updates_needed}")
            
            if updates_needed == 0:
                print("✅ Todos los registros ya usan CloudFront URLs")
                return True
            
            if dry_run:
                print(f"\n💡 Para aplicar los cambios, ejecuta el script con --apply")
                return True
            
            # Apply updates
            print(f"\n🔄 Aplicando {updates_needed} actualizaciones...")
            
            updated_count = 0
            for update in cloudfront_updates:
                try:
                    result = conn.execute(text(f"""
                        UPDATE {table_name} 
                        SET urlphoto = :new_url
                        WHERE idproducto = :product_id 
                        AND idphoto = :photo_id
                    """), {
                        'new_url': update['new_url'],
                        'product_id': update['product_id'],
                        'photo_id': update['photo_id']
                    })
                    
                    if result.rowcount > 0:
                        updated_count += 1
                        logger.debug(f"Updated product {update['product_id']}, photo {update['photo_id']}")
                    
                except Exception as e:
                    logger.error(f"Error updating product {update['product_id']}, photo {update['photo_id']}: {e}")
            
            # Commit changes
            conn.commit()
            
            print(f"✅ Actualizados {updated_count}/{updates_needed} registros exitosamente")
            return updated_count > 0
            
    except Exception as e:
        print(f"❌ Error actualizando tabla: {e}")
        return False

def update_all_tables_to_cloudfront(dry_run: bool = True):
    """
    Actualizar todas las tablas de búsqueda semántica a CloudFront
    
    Args:
        dry_run: Si True, solo muestra qué se actualizaría sin hacer cambios
    """
    print(f"🌐 {'[DRY RUN] ' if dry_run else ''}ACTUALIZACIÓN MASIVA A CLOUDFRONT")
    print("=" * 60)
    
    # Get database URL from environment
    database_url = os.getenv("DATABASE_URL")
    engine = create_engine(database_url)
    
    try:
        with engine.connect() as conn:
            # Find all semantic search tables
            result = conn.execute(text("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name LIKE 'imagenes_%'
                ORDER BY table_name
            """))
            
            tables = [row.table_name for row in result]
            
            if not tables:
                print("❌ No se encontraron tablas de búsqueda semántica")
                return
            
            print(f"📋 Encontradas {len(tables)} tablas de búsqueda semántica:")
            for table in tables:
                print(f"   - {table}")
            
            print()
            
            # Update each table
            total_updated = 0
            for table in tables:
                print(f"\n--- Procesando {table} ---")
                success = update_table_to_cloudfront(table, dry_run)
                if success and not dry_run:
                    total_updated += 1
            
            if not dry_run:
                print(f"\n🎉 Actualización completada: {total_updated}/{len(tables)} tablas procesadas")
            else:
                print(f"\n💡 Para aplicar los cambios, ejecuta: python {__file__} --apply")
            
    except Exception as e:
        print(f"❌ Error en actualización masiva: {e}")

def test_cloudfront_configuration():
    """
    Probar la configuración de CloudFront
    """
    print("🧪 PRUEBA DE CONFIGURACIÓN CLOUDFRONT")
    print("=" * 50)
    
    # Test configuration
    if not cloudfront_generator.validate_configuration():
        print("❌ Configuración de CloudFront inválida")
        return False
    
    print(f"✅ CloudFront base URL: {cloudfront_generator.get_cloudfront_base_url()}")
    
    # Test URL conversion
    test_s3_urls = [
        "https://dropi-core-colombia-dev.s3.us-east-2.amazonaws.com/colombia/products/129652/17023454071702345407colageno_hidrolizado_colagenox_2.jpg",
        "colombia/products/1/17031033281703103328gKYgkbm9fKeobmVpovDewFmwcUQv9kmNkvmopSjX.jpeg",
        "s3://dropi-core-colombia-dev/colombia/products/2/test.jpg"
    ]
    
    print(f"\n🔄 Pruebas de conversión de URL:")
    for s3_url in test_s3_urls:
        cloudfront_url = cloudfront_generator.convert_s3_url_to_cloudfront(s3_url)
        print(f"\n  S3:        {s3_url}")
        print(f"  CloudFront: {cloudfront_url}")
    
    return True

def main():
    """
    Función principal
    """
    import argparse
    
    parser = argparse.ArgumentParser(description="Actualizar URLs de S3 a CloudFront")
    parser.add_argument("--apply", action="store_true", help="Aplicar cambios (por defecto es dry-run)")
    parser.add_argument("--table", type=str, help="Actualizar solo una tabla específica")
    parser.add_argument("--test", action="store_true", help="Solo probar configuración")
    
    args = parser.parse_args()
    
    if args.test:
        test_cloudfront_configuration()
        return
    
    dry_run = not args.apply
    
    if args.table:
        update_table_to_cloudfront(args.table, dry_run)
    else:
        update_all_tables_to_cloudfront(dry_run)

if __name__ == "__main__":
    main()
