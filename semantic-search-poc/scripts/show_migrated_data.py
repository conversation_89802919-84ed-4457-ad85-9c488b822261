#!/usr/bin/env python3
"""
Show Migrated Data Script

Script para mostrar los datos reales migrados en la base de datos semántica.
"""

import sys
import os
from sqlalchemy import create_engine, text
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def show_migrated_data():
    """
    Mostrar los datos migrados en la tabla de búsqueda semántica
    """
    print("🔍 DATOS MIGRADOS EN LA BASE DE DATOS SEMÁNTICA")
    print("=" * 60)
    
    # Get database URL from environment
    database_url = os.getenv("DATABASE_URL")
    engine = create_engine(database_url)
    
    try:
        with engine.connect() as conn:
            # Get total count
            result = conn.execute(text('SELECT COUNT(*) FROM imagenes_dropi_colombia'))
            total_count = result.scalar()
            print(f"📊 Total de registros: {total_count}")
            
            # Show all migrated products with correct column names
            print(f"\n🛍️ PRODUCTOS MIGRADOS:")
            result = conn.execute(text("""
                SELECT idproducto, idphoto, 
                       LEFT(urlphoto, 80) as url_corta,
                       LEFT(texto, 150) as descripcion_corta,
                       empresa
                FROM imagenes_dropi_colombia 
                ORDER BY idproducto, idphoto
            """))
            
            current_product = None
            for row in result:
                if current_product != row.idproducto:
                    current_product = row.idproducto
                    print(f"\n--- PRODUCTO {row.idproducto} ---")
                
                print(f"  📸 Foto {row.idphoto}")
                print(f"     URL: {row.url_corta}...")
                print(f"     Descripción: {row.descripcion_corta}...")
                print(f"     Empresa: {row.empresa}")
            
            # Check for our specific migrated products
            migrated_ids = [1, 2, 3, 4, 5, 6, 7, 9]
            print(f"\n🎯 VERIFICACIÓN DE PRODUCTOS ESPECÍFICOS:")
            print(f"   IDs esperados: {migrated_ids}")
            
            result = conn.execute(text("""
                SELECT idproducto, COUNT(*) as foto_count
                FROM imagenes_dropi_colombia 
                WHERE idproducto IN (1, 2, 3, 4, 5, 6, 7, 9)
                GROUP BY idproducto
                ORDER BY idproducto
            """))
            
            found_products = []
            for row in result:
                found_products.append(row.idproducto)
                print(f"   ✅ Producto {row.idproducto}: {row.foto_count} fotos")
            
            missing_products = set(migrated_ids) - set(found_products)
            if missing_products:
                print(f"   ❌ Productos faltantes: {list(missing_products)}")
            else:
                print(f"   🎉 Todos los productos esperados están presentes!")
            
            # Show unique product IDs in the table
            result = conn.execute(text("""
                SELECT DISTINCT idproducto 
                FROM imagenes_dropi_colombia 
                ORDER BY idproducto
            """))
            
            all_product_ids = [row.idproducto for row in result]
            print(f"\n📋 Todos los IDs de productos en la tabla: {all_product_ids}")
            
            # Show embedding status
            result = conn.execute(text("""
                SELECT 
                    COUNT(*) as total,
                    COUNT(embedding) as con_embedding,
                    COUNT(*) - COUNT(embedding) as sin_embedding
                FROM imagenes_dropi_colombia
            """))
            
            row = result.fetchone()
            print(f"\n🧠 Estado de embeddings:")
            print(f"   Total registros: {row.total}")
            print(f"   Con embedding: {row.con_embedding}")
            print(f"   Sin embedding: {row.sin_embedding}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    show_migrated_data()
