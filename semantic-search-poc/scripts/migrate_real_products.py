#!/usr/bin/env python3
"""
Migrate Real Products Script

Script para migrar los productos reales extraídos de la base de datos de desarrollo
al sistema de búsqueda semántica.
"""

import sys
import os
import asyncio
import json
import logging
from datetime import datetime

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'app'))

from database.dev_database import ProductData, ProductPhotoData, ProductWithPhotos
from services.embedding_service import embedding_service
from services.llm_service import llm_service
from utils.validation import data_validator
from utils.cloudfront import cloudfront_generator

# Import db_manager directly
import importlib.util
db_module_path = os.path.join(os.path.dirname(__file__), '..', 'app', 'database.py')
spec = importlib.util.spec_from_file_location("database", db_module_path)
database_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(database_module)
db_manager = database_module.db_manager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_extracted_products(filename: str = "output/extracted_data.json"):
    """
    Cargar productos extraídos desde el archivo JSON
    """
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        products = []
        for item in data:
            product = ProductWithPhotos(
                product=ProductData(**item["product"]),
                photos=[ProductPhotoData(**photo) for photo in item["photos"]]
            )
            products.append(product)
        
        logger.info(f"✅ Cargados {len(products)} productos desde {filename}")
        return products
        
    except Exception as e:
        logger.error(f"❌ Error cargando productos: {e}")
        return []

async def migrate_real_products():
    """
    Migrar productos reales al sistema de búsqueda semántica
    """
    logger.info("🚀 Iniciando migración de productos reales")
    
    # Cargar productos extraídos
    products = load_extracted_products()
    if not products:
        logger.error("❌ No se pudieron cargar productos")
        return False
    
    # Configurar servicios - MIGRAR A MÉXICO
    company_name = "mexico"
    
    # Verificar conexión a base de datos objetivo
    if not db_manager.test_connection():
        logger.error("❌ No se puede conectar a la base de datos objetivo")
        return False
    
    # Crear tabla de empresa si no existe
    if not db_manager.create_company_table(company_name):
        logger.error("❌ No se pudo crear la tabla de empresa")
        return False
    
    logger.info(f"✅ Tabla de empresa '{company_name}' lista")
    
    successful_products = 0
    total_photos_processed = 0
    
    for i, product in enumerate(products, 1):
        logger.info(f"\n--- Producto {i}/{len(products)}: {product.product.name} ---")
        
        try:
            # Validar producto
            validation = data_validator.validate_product_data(product.product)
            if not validation.is_valid:
                logger.warning(f"⚠️ Producto {product.product.id} falló validación: {validation.errors}")
                continue
            
            # Procesar cada foto
            photos_stored = 0
            for photo in product.photos:
                try:
                    # Construir URL completa de S3 y convertir a CloudFront
                    s3_url = f"https://dropi-core-colombia-dev.s3.us-east-2.amazonaws.com/{photo.url_s3}"
                    cloudfront_url = cloudfront_generator.convert_s3_url_to_cloudfront(s3_url)

                    # Usar CloudFront URL si está disponible, sino usar S3 URL
                    final_url = cloudfront_url if cloudfront_url else s3_url
                    logger.info(f"📸 URL de imagen: {final_url}")
                    
                    # Generar descripción mejorada usando datos del producto
                    enhanced_description = llm_service.generate_enhanced_product_description(
                        product_name=product.product.name,
                        supplier_description=product.product.description,
                        dropi_description=product.product.dropi_app_description,
                        image_bytes=None,  # No descargar imagen por ahora
                        price=product.product.sale_price
                    )
                    
                    if not enhanced_description:
                        # Usar descripción de fallback
                        descriptions = []
                        if product.product.name:
                            descriptions.append(product.product.name)
                        if product.product.description:
                            # Limpiar HTML tags si existen
                            clean_desc = product.product.description.replace('<p>', '').replace('</p>', '').replace('<br>', ' ')
                            descriptions.append(clean_desc)
                        
                        enhanced_description = " ".join(descriptions) if descriptions else "Producto sin descripción"
                    
                    logger.info(f"📝 Descripción: {enhanced_description[:100]}...")
                    
                    # Generar embedding
                    embedding = embedding_service.get_embedding(enhanced_description)
                    if embedding is None:
                        logger.error(f"❌ No se pudo generar embedding para producto {product.product.id}")
                        continue
                    
                    # Almacenar en base de datos con CloudFront URL
                    success = db_manager.insert_image(
                        company_name=company_name,
                        id_producto=product.product.id,
                        id_photo=photo.id,
                        url_photo=final_url,
                        texto=enhanced_description,
                        embedding=embedding
                    )
                    
                    if success:
                        photos_stored += 1
                        total_photos_processed += 1
                        logger.info(f"✅ Foto {photo.id} almacenada exitosamente")
                    else:
                        logger.error(f"❌ Error almacenando foto {photo.id}")
                        
                except Exception as e:
                    logger.error(f"❌ Error procesando foto {photo.id}: {e}")
            
            if photos_stored > 0:
                successful_products += 1
                logger.info(f"✅ Producto {product.product.id} migrado exitosamente ({photos_stored} fotos)")
            else:
                logger.warning(f"⚠️ Producto {product.product.id} no se pudo migrar")
                    
        except Exception as e:
            logger.error(f"❌ Error procesando producto {product.product.id}: {e}")
    
    logger.info(f"\n🎉 Migración completada:")
    logger.info(f"  - Productos exitosos: {successful_products}/{len(products)}")
    logger.info(f"  - Fotos procesadas: {total_photos_processed}")
    
    # Probar búsqueda semántica
    if successful_products > 0:
        logger.info("\n🔍 Probando búsqueda semántica...")
        
        test_queries = [
            "reloj deportivo",
            "televisor Sony",
            "laptop computadora",
            "equipo de sonido"
        ]
        
        for query in test_queries:
            try:
                query_embedding = embedding_service.get_embedding(query)
                if query_embedding is not None:
                    results = db_manager.search_similar_images(
                        company_name=company_name,
                        query_embedding=query_embedding,
                        limit=3
                    )
                    
                    logger.info(f"  📍 '{query}': {len(results)} resultados")
                    for j, result in enumerate(results[:2], 1):
                        logger.info(f"    {j}. Producto {result.get('idProducto')} - Distancia: {result.get('distancia_coseno', 0):.3f}")
            except Exception as e:
                logger.error(f"Error en búsqueda '{query}': {e}")
    
    return successful_products > 0

async def main():
    """
    Función principal
    """
    try:
        success = await migrate_real_products()
        if success:
            logger.info("\n✅ Migración de productos reales completada exitosamente")
            logger.info("🎯 El sistema de búsqueda semántica está listo con datos reales")
        else:
            logger.error("\n❌ Migración falló")
            
    except Exception as e:
        logger.error(f"❌ Error en migración: {e}")

if __name__ == "__main__":
    asyncio.run(main())
