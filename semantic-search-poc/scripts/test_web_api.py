#!/usr/bin/env python3
"""
Test Web API Script

Script para probar la API web con el schema correcto y verificar que 
la búsqueda semántica funciona con los productos migrados.
"""

import requests
import json

def test_search_api():
    """
    Probar la API de búsqueda con el schema correcto
    """
    print("🔍 PROBANDO API DE BÚSQUEDA CON SCHEMA CORRECTO")
    print("=" * 60)
    
    base_url = "http://*************:8000"
    
    # Test queries for migrated products
    test_queries = [
        "hielera mágica",
        "juguete para perro", 
        "reloj militar",
        "cama de gato",
        "televisor Sony",
        "laptop Lenovo",
        "equipo de sonido",
        "reloj",
        "televisor",
        "laptop",
        "sonido"
    ]
    
    for query in test_queries:
        try:
            # Use correct schema: query_text instead of query
            search_data = {
                "company_name": "mexico",
                "query_text": query,
                "limit": 5
            }
            
            print(f"\n🔍 Buscando: '{query}'")
            
            response = requests.post(
                f"{base_url}/api/v1/search", 
                json=search_data,
                timeout=15
            )
            
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                search_results = response.json()
                results_count = len(search_results.get('results', []))
                print(f"   ✅ Resultados encontrados: {results_count}")
                
                if results_count > 0:
                    print(f"   📊 Mejores resultados:")
                    for i, result in enumerate(search_results['results'][:3], 1):
                        product_id = result.get('idProducto')
                        distance = result.get('distancia_coseno', 'N/A')
                        description = result.get('texto', '')[:80]
                        print(f"      {i}. Producto {product_id} (Distancia: {distance})")
                        print(f"         {description}...")
                else:
                    print(f"   ⚠️ No se encontraron resultados")
            else:
                print(f"   ❌ Error: {response.status_code}")
                try:
                    error_detail = response.json()
                    print(f"   Detalle: {error_detail}")
                except:
                    print(f"   Response: {response.text}")
                    
        except Exception as e:
            print(f"   ❌ Error de conexión: {e}")

def test_health_endpoint():
    """
    Probar el endpoint de salud
    """
    print(f"\n🏥 PROBANDO ENDPOINT DE SALUD")
    print("=" * 40)
    
    base_url = "http://*************:8000"
    
    try:
        response = requests.get(f"{base_url}/health", timeout=10)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            health_data = response.json()
            print(f"✅ Status: {health_data.get('status')}")
            print(f"✅ Database connected: {health_data.get('database_connected')}")
            print(f"✅ OpenAI configured: {health_data.get('openai_configured')}")
            print(f"✅ Version: {health_data.get('version')}")
        else:
            print(f"❌ Health check failed: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

def test_multi_vendor_search():
    """
    Probar búsqueda multi-vendor
    """
    print(f"\n🌐 PROBANDO BÚSQUEDA MULTI-VENDOR")
    print("=" * 40)
    
    base_url = "http://*************:8000"
    
    try:
        search_data = {
            "query_text": "televisor Sony",
            "limit": 5
        }
        
        response = requests.post(
            f"{base_url}/api/v1/search/all-vendors", 
            json=search_data,
            timeout=15
        )
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            search_results = response.json()
            results_count = len(search_results.get('results', []))
            print(f"✅ Resultados encontrados: {results_count}")
            
            if results_count > 0:
                print(f"📊 Resultados por empresa:")
                companies = {}
                for result in search_results['results']:
                    company = result.get('empresa', 'unknown')
                    if company not in companies:
                        companies[company] = 0
                    companies[company] += 1
                
                for company, count in companies.items():
                    print(f"   - {company}: {count} resultados")
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    """
    Función principal
    """
    print("🧪 PRUEBAS DE API WEB SEMÁNTICA")
    print("=" * 60)
    
    # Test health endpoint
    test_health_endpoint()
    
    # Test search API with correct schema
    test_search_api()
    
    # Test multi-vendor search
    test_multi_vendor_search()
    
    print("\n" + "=" * 60)
    print("🏁 PRUEBAS COMPLETADAS")

if __name__ == "__main__":
    main()
