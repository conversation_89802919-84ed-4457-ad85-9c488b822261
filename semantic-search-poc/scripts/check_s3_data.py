#!/usr/bin/env python3
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'app'))
from database.dev_database import dev_db_manager
from sqlalchemy import text

with dev_db_manager.dev_engine.connect() as conn:
    print('🔍 Verificando productos con URLs de S3...')
    result = conn.execute(text('''
        SELECT COUNT(DISTINCT product_id) 
        FROM product_photos 
        WHERE "urlS3" IS NOT NULL AND "urlS3" != ''
    '''))
    s3_products = result.scalar()
    print(f'Productos con URLs de S3: {s3_products}')
    
    print('\n📸 Muestra de URLs de S3:')
    result = conn.execute(text('''
        SELECT product_id, "urlS3"
        FROM product_photos 
        WHERE "urlS3" IS NOT NULL AND "urlS3" != ''
        LIMIT 5
    '''))
    
    for row in result:
        print(f'  - Producto {row.product_id}: {row.urlS3[:80]}...')
