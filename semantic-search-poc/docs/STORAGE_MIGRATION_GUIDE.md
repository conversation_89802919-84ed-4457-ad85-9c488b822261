# Storage Migration Guide: Local to AWS S3

This document provides a comprehensive guide for migrating from local file storage to AWS S3 for the Semantic Search POC application.

## Current Implementation

The application currently uses a **configurable storage service pattern** that allows easy switching between storage backends:

- **Local Storage**: Files stored in `uploads/` directory (development/testing)
- **AWS S3 Storage**: Files stored in S3 bucket (production)

## Storage Service Architecture

### Abstract Base Class
```python
class StorageService(ABC):
    @abstractmethod
    async def upload_file(self, file_content: BinaryIO, filename: str, content_type: str) -> str
    
    @abstractmethod
    async def delete_file(self, file_url: str) -> bool
    
    @abstractmethod
    def get_file_url(self, file_path: str) -> str
```

### Current Local Implementation
- **Directory Structure**: `uploads/images/` for image files
- **File Naming**: UUID-based unique filenames to prevent conflicts
- **URL Format**: `http://localhost:8000/uploads/images/{uuid}.{ext}`
- **Access**: Served via FastAPI StaticFiles middleware

### Future S3 Implementation
- **Bucket Structure**: `images/` prefix for image files
- **File Naming**: Same UUID-based naming convention
- **URL Format**: `https://{bucket}.s3.amazonaws.com/images/{uuid}.{ext}`
- **Access**: Direct S3 public URLs with proper ACL settings

## Migration Steps

### Step 1: AWS S3 Setup

1. **Create S3 Bucket**:
   ```bash
   aws s3 mb s3://your-semantic-search-bucket --region us-east-1
   ```

2. **Configure Bucket Policy** for public read access:
   ```json
   {
     "Version": "2012-10-17",
     "Statement": [
       {
         "Sid": "PublicReadGetObject",
         "Effect": "Allow",
         "Principal": "*",
         "Action": "s3:GetObject",
         "Resource": "arn:aws:s3:::your-semantic-search-bucket/*"
       }
     ]
   }
   ```

3. **Configure CORS** for web uploads:
   ```json
   [
     {
       "AllowedHeaders": ["*"],
       "AllowedMethods": ["GET", "PUT", "POST", "DELETE"],
       "AllowedOrigins": ["*"],
       "ExposeHeaders": []
     }
   ]
   ```

### Step 2: Environment Configuration

Update `.env` file to use S3:

```env
# Storage Configuration
STORAGE_TYPE=s3
S3_BUCKET_NAME=your-semantic-search-bucket
AWS_REGION=us-east-1
AWS_PROFILE=IA
```

### Step 3: Data Migration (Optional)

If you have existing local files to migrate:

```python
# Migration script example
import asyncio
from app.services.storage_service import LocalStorageService, S3StorageService

async def migrate_files():
    local_service = LocalStorageService()
    s3_service = S3StorageService("your-bucket", "us-east-1", "IA")
    
    # Migrate existing files
    for file_path in Path("uploads/images").glob("*"):
        with open(file_path, 'rb') as f:
            new_url = await s3_service.upload_file(
                f, file_path.name, "image/jpeg"
            )
            print(f"Migrated {file_path.name} -> {new_url}")
```

### Step 4: Database URL Updates

Update existing database records to use S3 URLs:

```sql
-- Update existing image URLs (if needed)
UPDATE IMAGENES_COLOMBIA 
SET urlPhoto = REPLACE(
    urlPhoto, 
    'http://localhost:8000/uploads/', 
    'https://your-bucket.s3.amazonaws.com/'
);
```

### Step 5: Application Restart

Simply restart the application - the storage service factory will automatically create an S3 service instance based on the environment configuration.

## Code Changes Required

**No code changes are required** for the migration! The application uses the factory pattern:

```python
# This automatically selects the right storage service
storage_service = StorageServiceFactory.create_storage_service()
```

## Testing the Migration

### 1. Test File Upload
```bash
curl -X POST "http://localhost:8000/api/v1/files/upload" \
  -F "file=@test-image.jpg" \
  -F "company=TEST" \
  -F "product_id=999" \
  -F "description=Test upload"
```

### 2. Verify S3 Storage
- Check AWS S3 console for uploaded files
- Verify public access to file URLs
- Test file deletion functionality

### 3. Test Web Interface
- Upload images through the web interface
- Verify images display correctly in search results
- Test image preview functionality

## Rollback Plan

To rollback to local storage:

1. Update `.env`:
   ```env
   STORAGE_TYPE=local
   ```

2. Restart the application

3. Optionally download S3 files back to local storage

## Performance Considerations

### Local Storage
- **Pros**: Fast access, no external dependencies, no costs
- **Cons**: Not scalable, single point of failure, no CDN

### S3 Storage
- **Pros**: Scalable, reliable, CDN integration possible, backup/versioning
- **Cons**: Network latency, costs, external dependency

## Security Considerations

### Local Storage
- Files served directly by application server
- Access controlled by application authentication

### S3 Storage
- Files publicly accessible via direct URLs
- Consider signed URLs for sensitive content
- Implement proper IAM policies

## Cost Estimation

### S3 Costs (approximate)
- **Storage**: $0.023 per GB/month (Standard)
- **Requests**: $0.0004 per 1,000 PUT requests
- **Data Transfer**: $0.09 per GB (first 10TB/month)

### Example Monthly Cost
- 1,000 images (10MB each) = 10GB storage = $0.23/month
- 1,000 uploads = $0.0004
- **Total**: ~$0.25/month for moderate usage

## Monitoring and Maintenance

### CloudWatch Metrics
- Monitor S3 bucket size and request counts
- Set up alerts for unusual activity
- Track costs with AWS Cost Explorer

### Application Logs
- Monitor upload success/failure rates
- Track file access patterns
- Log storage service errors

## Future Enhancements

1. **CDN Integration**: Add CloudFront for faster global access
2. **Image Processing**: Automatic resizing/optimization
3. **Backup Strategy**: Cross-region replication
4. **Signed URLs**: For private/temporary access
5. **Lifecycle Policies**: Automatic archival of old files

## Conclusion

The configurable storage service pattern provides a seamless migration path from local to S3 storage with zero code changes required. The migration can be performed gradually and rolled back easily if needed.
