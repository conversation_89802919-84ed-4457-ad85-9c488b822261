# Web Interface Testing Guide

This document provides comprehensive testing instructions for the Semantic Search POC web interface.

## Overview

The web interface provides a complete user-friendly frontend for the semantic search system with the following features:

- **Company Management**: Create and manage company/country databases
- **Image Upload**: Upload product images with descriptions
- **Semantic Search**: Perform intelligent text-based product searches
- **Real-time Results**: View search results with similarity scores
- **Responsive Design**: Works on desktop and mobile devices

## Prerequisites

1. **Application Running**: Ensure the FastAPI application is running on `http://localhost:8000`
2. **Database Ready**: PostgreSQL with pgvector extension enabled
3. **AWS Configured**: AWS Bedrock access with profile "IA"
4. **Browser**: Modern web browser (Chrome, Firefox, Safari, Edge)

## Accessing the Interface

Open your web browser and navigate to: `http://localhost:8000`

You should see the Semantic Search POC interface with a modern gradient design.

## Testing Procedures

### 1. Company Management Testing

#### Test 1.1: Create New Company
1. **Navigate** to the "Gestión de Empresas" section
2. **Enter** a company name (e.g., "MEXICO", "BRASIL", "ARGENTINA")
3. **Click** "Crear Empresa" button
4. **Expected Result**: 
   - Green success notification appears
   - Company statistics update
   - New company appears in dropdown menus

#### Test 1.2: Duplicate Company Handling
1. **Try** to create a company that already exists (e.g., "COLOMBIA")
2. **Expected Result**: Appropriate error message displayed

#### Test 1.3: Invalid Company Name
1. **Leave** company name field empty
2. **Click** "Crear Empresa"
3. **Expected Result**: Error notification about required field

### 2. Image Upload Testing

#### Test 2.1: Complete Product Upload
1. **Select** a company from the dropdown
2. **Enter** a unique Product ID (e.g., 100)
3. **Enter** Photo ID (default: 1)
4. **Click** "Seleccionar imagen" and choose an image file (JPG, PNG, WebP)
5. **Verify** image preview appears
6. **Enter** detailed product description
7. **Click** "Subir Producto"
8. **Expected Result**:
   - Image uploads successfully
   - Product created with embedding
   - Success notification with embedding dimension
   - Form clears automatically

#### Test 2.2: Upload Without Image
1. **Fill** all fields except image
2. **Click** "Subir Producto"
3. **Expected Result**: Product created with placeholder image

#### Test 2.3: Invalid File Type
1. **Try** uploading a non-image file (e.g., .txt, .pdf)
2. **Expected Result**: Error message about file type

#### Test 2.4: Large File Upload
1. **Try** uploading an image larger than 10MB
2. **Expected Result**: Error message about file size

#### Test 2.5: Missing Required Fields
1. **Leave** Product ID or description empty
2. **Click** "Subir Producto"
3. **Expected Result**: Error notification about required fields

### 3. Semantic Search Testing

#### Test 3.1: Basic Search
1. **Select** a company with existing products (e.g., "COLOMBIA")
2. **Enter** search query: "zapatos negros"
3. **Click** "Buscar"
4. **Expected Result**:
   - Search results appear with relevant products
   - Similarity scores displayed
   - Results sorted by relevance

#### Test 3.2: Advanced Search Queries
Test various search queries:
- "vestido azul elegante"
- "calzado formal mujer"
- "ropa trabajo profesional"
- "zapatos tacón alto"

#### Test 3.3: Empty Search
1. **Leave** search field empty
2. **Click** "Buscar"
3. **Expected Result**: Error message about required search query

#### Test 3.4: No Results Search
1. **Search** for something unlikely: "producto inexistente xyz123"
2. **Expected Result**: "No se encontraron resultados" message

#### Test 3.5: Different Result Limits
1. **Test** with 5, 10, and 20 result limits
2. **Verify** correct number of results returned

#### Test 3.6: Cross-Company Search
1. **Search** in a company with no products
2. **Expected Result**: No results message

### 4. User Interface Testing

#### Test 4.1: Responsive Design
1. **Resize** browser window to different sizes
2. **Test** on mobile device or use browser dev tools
3. **Expected Result**: Interface adapts to screen size

#### Test 4.2: Form Validation
1. **Test** all form fields with invalid data
2. **Verify** appropriate error messages
3. **Check** field highlighting for errors

#### Test 4.3: Loading States
1. **Monitor** loading indicators during operations
2. **Verify** buttons disable during processing
3. **Check** loading animations appear

#### Test 4.4: Notifications
1. **Verify** success notifications appear and auto-dismiss
2. **Check** error notifications display properly
3. **Test** notification positioning and styling

### 5. Integration Testing

#### Test 5.1: End-to-End Workflow
1. **Create** a new company
2. **Upload** multiple products with images
3. **Perform** searches for uploaded products
4. **Verify** results match uploaded content

#### Test 5.2: Image Display
1. **Upload** products with real images
2. **Perform** searches
3. **Verify** images display correctly in results
4. **Test** image error handling (broken URLs)

#### Test 5.3: Multiple Companies
1. **Create** multiple companies
2. **Upload** products to each
3. **Verify** search isolation between companies

### 6. Performance Testing

#### Test 6.1: Upload Performance
1. **Upload** multiple large images
2. **Monitor** upload times
3. **Verify** system remains responsive

#### Test 6.2: Search Performance
1. **Perform** multiple searches rapidly
2. **Monitor** response times
3. **Check** for memory leaks or slowdowns

#### Test 6.3: Concurrent Users
1. **Open** multiple browser tabs
2. **Perform** operations simultaneously
3. **Verify** no conflicts or errors

## Expected Results Summary

### Successful Operations Should Show:
- ✅ Green success notifications
- ✅ Updated interface elements
- ✅ Proper data display
- ✅ Smooth animations and transitions

### Error Conditions Should Show:
- ❌ Red error notifications
- ❌ Clear error messages
- ❌ Form field highlighting
- ❌ Graceful degradation

## Troubleshooting Common Issues

### Issue: Interface Not Loading
- **Check**: Application is running on port 8000
- **Verify**: No console errors in browser dev tools
- **Solution**: Restart application, check logs

### Issue: Upload Failures
- **Check**: File size and type restrictions
- **Verify**: Network connectivity
- **Solution**: Try smaller files, check server logs

### Issue: Search Not Working
- **Check**: Company has products
- **Verify**: Database connection
- **Solution**: Add products first, check backend logs

### Issue: Images Not Displaying
- **Check**: Image URLs are accessible
- **Verify**: File upload completed successfully
- **Solution**: Re-upload images, check storage service

## Browser Compatibility

### Tested Browsers:
- ✅ Chrome 120+
- ✅ Firefox 115+
- ✅ Safari 16+
- ✅ Edge 120+

### Required Features:
- JavaScript ES6+ support
- Fetch API
- FormData API
- CSS Grid and Flexbox
- File API

## API Endpoints Used

The web interface interacts with these backend endpoints:

- `GET /health` - Health check
- `POST /api/v1/companies/{company}/create` - Create company
- `POST /api/v1/companies/{company}/images` - Add product
- `POST /api/v1/search` - Semantic search
- `POST /api/v1/files/upload` - File upload
- `GET /uploads/{path}` - Serve uploaded files

## Security Considerations

### Current Implementation:
- Client-side file validation
- Server-side file type checking
- File size limits (10MB)
- No authentication (POC only)

### Production Recommendations:
- Add user authentication
- Implement rate limiting
- Add CSRF protection
- Validate all inputs server-side
- Use HTTPS only

## Performance Metrics

### Target Performance:
- Page load: < 2 seconds
- Search response: < 3 seconds
- Image upload: < 10 seconds (per 5MB)
- UI interactions: < 100ms

### Monitoring:
- Use browser dev tools Network tab
- Monitor server response times
- Check memory usage over time
- Verify no JavaScript errors

## Conclusion

This testing guide ensures comprehensive validation of the web interface functionality. Regular testing helps maintain quality and user experience as the application evolves.
