<!DOCTYPE html>
<html lang="es">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Semantic Search POC - Automatic Image Description</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .main-content {
            padding: 40px;
        }

        .section {
            margin-bottom: 40px;
            padding: 30px;
            border-radius: 15px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
        }

        .section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.8rem;
            font-weight: 500;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #495057;
        }

        .form-control {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(79, 172, 254, 0.3);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-success:hover {
            background: #218838;
            transform: translateY(-2px);
        }

        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }

        .image-preview {
            max-width: 200px;
            max-height: 200px;
            border-radius: 8px;
            margin-top: 10px;
            border: 2px solid #e9ecef;
        }

        .results-container {
            margin-top: 30px;
        }

        .result-item {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border-left: 4px solid #4facfe;
        }

        .result-item img {
            max-width: 150px;
            max-height: 150px;
            border-radius: 8px;
            float: left;
            margin-right: 20px;
            margin-bottom: 10px;
        }

        .result-content {
            overflow: hidden;
        }

        .result-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .result-description {
            color: #6c757d;
            line-height: 1.6;
            margin-bottom: 10px;
        }

        .similarity-score {
            background: #e3f2fd;
            color: #1976d2;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            display: inline-block;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }

        .loading::after {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #4facfe;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .company-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #4facfe;
        }

        .stat-label {
            color: #6c757d;
            margin-top: 5px;
        }

        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }

            .header h1 {
                font-size: 2rem;
            }

            .main-content {
                padding: 20px;
            }

            .result-item img {
                float: none;
                display: block;
                margin: 0 auto 15px auto;
            }
        }

        .file-input-wrapper {
            position: relative;
            display: inline-block;
            cursor: pointer;
            width: 100%;
        }

        .file-input-wrapper input[type=file] {
            position: absolute;
            left: -9999px;
        }

        .file-input-display {
            display: block;
            padding: 12px 16px;
            border: 2px dashed #4facfe;
            border-radius: 8px;
            text-align: center;
            background: #f8f9ff;
            color: #4facfe;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .file-input-display:hover {
            background: #e3f2fd;
            border-color: #1976d2;
        }

        .hidden {
            display: none;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Semantic Search POC</h1>
            <p>Sistema de búsqueda semántica con inteligencia artificial</p>
        </div>

        <div class="main-content">

            <!-- Image Upload Section with Automatic Description -->
            <div class="section">
                <h2>📤 Subir Producto con Descripción Automática</h2>
                <div class="grid">
                    <div>
                        <div class="form-group">
                            <label for="productCompany">Empresa:</label>
                            <select id="productCompany" class="form-control">
                                <option value="COLOMBIA">COLOMBIA</option>
                                <option value="MEXICO">MEXICO</option>
                                <option value="BRASIL">BRASIL</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <div class="alert alert-success" style="margin: 0; padding: 10px; font-size: 14px;">
                                <strong>🤖 Descripción Automática:</strong> Amazon Nova Micro generará automáticamente
                                la descripción del producto.
                            </div>
                        </div>
                    </div>
                    <div>
                        <div class="form-group">
                            <label for="imageFile">Imagen del Producto:</label>
                            <div class="file-input-wrapper">
                                <input type="file" id="imageFile" accept="image/jpeg" onchange="previewImage(this)">
                                <label for="imageFile" class="file-input-display">
                                    📁 Seleccionar imagen (JPEG solamente)
                                </label>
                            </div>
                            <img id="imagePreview" class="image-preview hidden" alt="Vista previa">
                        </div>
                        <button onclick="generateDescription()" class="btn btn-primary" id="generateBtn">🤖 Analizar
                            Imagen</button>
                    </div>
                </div>

                <!-- Auto-generated Description Preview -->
                <div id="descriptionPreview" class="section hidden" style="margin-top: 20px; background: #e3f2fd;">
                    <h3>📝 Descripción Generada Automáticamente</h3>
                    <div class="form-group">
                        <label for="generatedDescription">Descripción (Amazon Nova Micro):</label>
                        <textarea id="generatedDescription" class="form-control" rows="4" readonly
                            style="background: #f8f9fa;"></textarea>
                    </div>
                    <div class="form-group">
                        <button onclick="confirmAndSave()" class="btn btn-success">✅ Confirmar y Guardar
                            Producto</button>
                        <button onclick="cancelGeneration()" class="btn btn-secondary">❌ Cancelar</button>
                    </div>
                </div>
            </div>

            <!-- Search Section -->
            <div class="section">
                <h2>🔍 Búsqueda Semántica</h2>
                <div class="grid">
                    <div>
                        <div class="form-group">
                            <label for="searchQuery">Consulta de Búsqueda:</label>
                            <input type="text" id="searchQuery" class="form-control"
                                placeholder="Ej: luces LED, zapatos negros, vestido azul">
                        </div>
                        <div class="form-group">
                            <label for="searchCompany">Empresa:</label>
                            <select id="searchCompany" class="form-control">
                                <option value="COLOMBIA">COLOMBIA</option>
                                <option value="MEXICO">MEXICO</option>
                                <option value="BRASIL">BRASIL</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="searchLimit">Número de Resultados:</label>
                            <select id="searchLimit" class="form-control">
                                <option value="5">5 resultados</option>
                                <option value="10">10 resultados</option>
                                <option value="20">20 resultados</option>
                            </select>
                        </div>
                        <button onclick="performSearch()" class="btn btn-primary">🔍 Buscar</button>
                    </div>
                    <div>
                        <div id="searchInfo" class="alert alert-success hidden">
                            <strong>Búsqueda completada:</strong> <span id="searchStats"></span>
                        </div>
                        <div id="searchError" class="alert alert-error hidden">
                            <strong>Error:</strong> <span id="errorMessage"></span>
                        </div>
                    </div>
                </div>

                <div id="searchResults" class="results-container">
                    <!-- Search results will be displayed here -->
                </div>
            </div>

            <!-- View Existing Products Section -->
            <div class="section">
                <h2>📋 Ver Productos Existentes</h2>
                <div class="grid">
                    <div>
                        <div class="form-group">
                            <label for="viewCompany">Empresa:</label>
                            <select id="viewCompany" class="form-control" onchange="loadExistingProducts()">
                                <option value="COLOMBIA">COLOMBIA</option>
                                <option value="MEXICO">MEXICO</option>
                                <option value="BRASIL">BRASIL</option>
                            </select>
                        </div>
                        <button onclick="loadExistingProducts()" class="btn btn-primary">🔄 Actualizar Lista</button>
                    </div>
                    <div>
                        <div id="productsInfo" class="alert alert-success hidden">
                            <strong>Productos cargados:</strong> <span id="productsStats"></span>
                        </div>
                        <div id="productsError" class="alert alert-error hidden">
                            <strong>Error:</strong> <span id="productsErrorMessage"></span>
                        </div>
                    </div>
                </div>

                <div id="existingProducts" class="results-container">
                    <div class="loading">Cargando productos existentes...</div>
                </div>
            </div>

        </div>
    </div>

    <!-- Notification area -->
    <div id="notifications" style="position: fixed; top: 20px; right: 20px; z-index: 1000;"></div>

    <script>
        // Global variables
        let currentImageFile = null;
        let tempProductData = null;
        const API_BASE = '';

        // Utility functions
        function showNotification(message, type = 'success') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type}`;
            notification.style.cssText = 'margin-bottom: 10px; max-width: 400px; animation: slideIn 0.3s ease;';
            notification.innerHTML = `<strong>${type === 'success' ? 'Éxito:' : 'Error:'}</strong> ${message}`;

            document.getElementById('notifications').appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => notification.remove(), 300);
            }, 5000);
        }

        function showLoading(elementId) {
            document.getElementById(elementId).innerHTML = '<div class="loading">Cargando...</div>';
        }

        function hideElement(elementId) {
            document.getElementById(elementId).classList.add('hidden');
        }

        function showElement(elementId) {
            document.getElementById(elementId).classList.remove('hidden');
        }

        // Image preview function
        function previewImage(input) {
            const preview = document.getElementById('imagePreview');
            const fileDisplay = input.nextElementSibling;

            if (input.files && input.files[0]) {
                currentImageFile = input.files[0];
                const reader = new FileReader();

                reader.onload = function (e) {
                    preview.src = e.target.result;
                    showElement('imagePreview');
                };

                reader.readAsDataURL(input.files[0]);
                fileDisplay.textContent = `📁 ${input.files[0].name}`;
            } else {
                currentImageFile = null;
                hideElement('imagePreview');
                fileDisplay.textContent = '📁 Seleccionar imagen (JPEG solamente)';
            }
        }

        // Automatic description generation functions
        async function generateDescription() {
            const company = document.getElementById('productCompany').value;

            if (!currentImageFile) {
                showNotification('Por favor selecciona una imagen primero', 'error');
                return;
            }

            if (!company) {
                showNotification('Por favor selecciona una empresa', 'error');
                return;
            }

            try {
                const generateBtn = document.getElementById('generateBtn');
                const originalText = generateBtn.textContent;
                generateBtn.textContent = '🤖 Analizando con Nova Micro...';
                generateBtn.disabled = true;

                const formData = new FormData();
                formData.append('file', currentImageFile);
                formData.append('company', company);

                const response = await fetch(`${API_BASE}/api/v1/files/generate-description`, {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.detail || `Error del servidor: ${response.status}`);
                }

                const result = await response.json();

                if (result.success) {
                    // Store temporary data
                    tempProductData = {
                        file: currentImageFile,
                        company: company,
                        description: result.descripcion,
                        filename: result.filename
                    };

                    // Show generated description
                    document.getElementById('generatedDescription').value = result.descripcion;
                    showElement('descriptionPreview');
                    showNotification('Descripción generada exitosamente con Amazon Nova Micro');
                } else {
                    throw new Error(result.message || 'Error al generar descripción');
                }

                generateBtn.textContent = originalText;
                generateBtn.disabled = false;

            } catch (error) {
                console.error('Error:', error);
                showNotification('Error al generar descripción: ' + error.message, 'error');

                const generateBtn = document.getElementById('generateBtn');
                generateBtn.textContent = '🤖 Analizar Imagen';
                generateBtn.disabled = false;
            }
        }

        // Product save functions
        async function confirmAndSave() {
            if (!tempProductData) {
                showNotification('No hay datos de producto para guardar', 'error');
                return;
            }

            try {
                // First, ensure company table exists
                await ensureCompanyExists(tempProductData.company);

                // Get next available product ID
                const idResponse = await fetch(`${API_BASE}/api/v1/companies/${tempProductData.company}/next-product-id`);
                const idResult = await idResponse.json();

                if (!idResult.success) {
                    showNotification('Error al obtener ID del producto: ' + (idResult.message || 'Error desconocido'), 'error');
                    return;
                }

                const productId = idResult.next_product_id;
                const photoId = 1; // Default to 1 for new products

                // Upload image file
                const formData = new FormData();
                formData.append('file', tempProductData.file);
                formData.append('company', tempProductData.company);
                formData.append('description', tempProductData.description);
                formData.append('product_id', productId);
                formData.append('photo_id', photoId);

                const uploadResponse = await fetch(`${API_BASE}/api/v1/files/upload`, {
                    method: 'POST',
                    body: formData
                });

                const uploadResult = await uploadResponse.json();

                if (!uploadResult.success) {
                    showNotification('Error al subir la imagen', 'error');
                    return;
                }

                // Create product entry with image URL
                const productData = {
                    idProducto: parseInt(productId),
                    idPhoto: parseInt(photoId),
                    urlPhoto: uploadResult.file_url,
                    texto: tempProductData.description
                };

                const response = await fetch(`${API_BASE}/api/v1/companies/${tempProductData.company}/images`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(productData)
                });

                const result = await response.json();

                if (result.success) {
                    showNotification(`Producto #${productId} guardado exitosamente con descripción automática (Embedding: ${result.embedding_dimension}D)`);
                    clearProductForm();
                } else {
                    showNotification(result.message || 'Error al crear el producto', 'error');
                }

            } catch (error) {
                showNotification('Error de conexión al guardar el producto', 'error');
                console.error('Error:', error);
            }
        }

        async function ensureCompanyExists(companyName) {
            try {
                const response = await fetch(`${API_BASE}/api/v1/companies/${companyName}/create`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({})
                });
                // Don't throw error if company already exists
                const result = await response.json();
                console.log(`Company ${companyName} status:`, result.message);
            } catch (error) {
                console.log('Company creation check:', error.message);
            }
        }

        function cancelGeneration() {
            tempProductData = null;
            hideElement('descriptionPreview');
            document.getElementById('generatedDescription').value = '';
        }

        function clearProductForm() {
            document.getElementById('imageFile').value = '';
            currentImageFile = null;
            tempProductData = null;
            hideElement('imagePreview');
            hideElement('descriptionPreview');
            document.querySelector('.file-input-display').textContent = '📁 Seleccionar imagen (JPEG solamente)';
            document.getElementById('generatedDescription').value = '';
        }

        // Product display functions
        async function loadExistingProducts() {
            const company = document.getElementById('viewCompany').value;

            hideElement('productsInfo');
            hideElement('productsError');
            showLoading('existingProducts');

            try {
                const response = await fetch(`${API_BASE}/api/v1/companies/${company}/products`);
                const result = await response.json();

                if (result.success) {
                    displayExistingProducts(result);
                    showProductsInfo(result);
                } else {
                    showProductsError(result.message || 'Error al cargar productos');
                }
            } catch (error) {
                showProductsError('Error de conexión al cargar productos');
                console.error('Error:', error);
            }
        }

        function displayExistingProducts(result) {
            const productsContainer = document.getElementById('existingProducts');

            if (!result.products || result.products.length === 0) {
                productsContainer.innerHTML = `
                    <div class="result-item">
                        <div class="result-content">
                            <div class="result-title">No hay productos en ${result.company_name}</div>
                            <div class="result-description">
                                Esta empresa aún no tiene productos cargados. Usa la sección "Subir Producto" para agregar el primer producto.
                            </div>
                        </div>
                    </div>
                `;
                return;
            }

            const productsHTML = result.products.map(product => {
                return `
                    <div class="result-item">
                        <img src="${product.urlPhoto}" alt="Producto ${product.idProducto}" onerror="this.src='https://via.placeholder.com/150x150?text=Sin+Imagen'">
                        <div class="result-content">
                            <div class="result-title">Producto #${product.idProducto} - Foto #${product.idPhoto}</div>
                            <div class="result-description">${product.texto || 'Sin descripción'}</div>
                            <div class="similarity-score">Empresa: ${product.empresa}</div>
                        </div>
                    </div>
                `;
            }).join('');

            productsContainer.innerHTML = productsHTML;
        }

        function showProductsInfo(result) {
            const productsStats = document.getElementById('productsStats');
            productsStats.textContent = `${result.total_products} productos encontrados en ${result.company_name}`;
            showElement('productsInfo');
        }

        function showProductsError(message) {
            const errorMessage = document.getElementById('productsErrorMessage');
            errorMessage.textContent = message;
            showElement('productsError');
            document.getElementById('existingProducts').innerHTML = '';
        }

        // Search functions
        async function performSearch() {
            const query = document.getElementById('searchQuery').value.trim();
            const company = document.getElementById('searchCompany').value;
            const limit = parseInt(document.getElementById('searchLimit').value);

            if (!query) {
                showNotification('Por favor ingresa una consulta de búsqueda', 'error');
                return;
            }

            hideElement('searchInfo');
            hideElement('searchError');
            showLoading('searchResults');

            const searchData = {
                company_name: company,
                query_text: query,
                limit: limit
            };

            try {
                const response = await fetch(`${API_BASE}/api/v1/search`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(searchData)
                });

                const result = await response.json();

                if (result.success) {
                    displaySearchResults(result);
                    showSearchInfo(result);
                } else {
                    showSearchError(result.message || 'Error en la búsqueda');
                }
            } catch (error) {
                showSearchError('Error de conexión durante la búsqueda');
                console.error('Error:', error);
            }
        }

        function displaySearchResults(result) {
            const resultsContainer = document.getElementById('searchResults');

            if (!result.results || result.results.length === 0) {
                resultsContainer.innerHTML = `
                    <div class="result-item">
                        <div class="result-content">
                            <div class="result-title">No se encontraron resultados</div>
                            <div class="result-description">
                                Intenta con diferentes términos de búsqueda o verifica que existan productos en la empresa seleccionada.
                            </div>
                        </div>
                    </div>
                `;
                return;
            }

            const resultsHTML = result.results.map(item => {
                // La distancia coseno está en el rango [0, 2].
                // La normalizamos al rango [0, 1] dividiendo por 2.
                // Luego la restamos de 1 para obtener una similitud en el rango [0, 1].
                const similarityValue = (1 - (item.distancia_coseno / 2));

                const similarity = similarityValue.toFixed(3);
                const similarityPercent = (similarityValue * 100).toFixed(1);

                return `
                    <div class="result-item">
                        <img src="${item.urlPhoto}" alt="Producto ${item.idProducto}" onerror="this.src='https://via.placeholder.com/150x150?text=Sin+Imagen'">
                        <div class="result-content">
                            <div class="result-title">Producto #${item.idProducto} - Foto #${item.idPhoto}</div>
                            <div class="result-description">${item.texto}</div>
                            <div class="similarity-score">Similitud: ${similarityPercent}% (${similarity})</div>
                        </div>
                    </div>
                `;
            }).join('');

            resultsContainer.innerHTML = resultsHTML;
        }

        function showSearchInfo(result) {
            const searchStats = document.getElementById('searchStats');
            searchStats.textContent = `${result.total_results} resultados encontrados para "${result.query_text}" en ${result.company_name}`;
            showElement('searchInfo');
        }

        function showSearchError(message) {
            const errorMessage = document.getElementById('errorMessage');
            errorMessage.textContent = message;
            showElement('searchError');
            document.getElementById('searchResults').innerHTML = '';
        }

        // Company selection synchronization
        function syncCompanySelections(selectedCompany) {
            document.getElementById('viewCompany').value = selectedCompany;
            document.getElementById('productCompany').value = selectedCompany;
            document.getElementById('searchCompany').value = selectedCompany;
            loadExistingProducts(); // Refresh products when company changes
        }

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function () {
            // Load existing products on page load
            loadExistingProducts();

            // Add Enter key support for search
            document.getElementById('searchQuery').addEventListener('keypress', function (e) {
                if (e.key === 'Enter') {
                    performSearch();
                }
            });

            // Synchronize company selections across all sections
            document.getElementById('viewCompany').addEventListener('change', function (e) {
                syncCompanySelections(e.target.value);
            });

            document.getElementById('productCompany').addEventListener('change', function (e) {
                syncCompanySelections(e.target.value);
            });

            document.getElementById('searchCompany').addEventListener('change', function (e) {
                syncCompanySelections(e.target.value);
            });
        });

        // Add CSS animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>

</html>