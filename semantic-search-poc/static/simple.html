<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo de Búsqueda Semántica - Descripción Automática</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .card { box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: none; }
        .card-header { background-color: #f1f8ff; border-bottom: 1px solid #e3f2fd; }
        #descriptionPreview { animation: fadeIn 0.3s ease-in; }
        #generatedDescription { 
            font-style: italic; color: #495057; background-color: #f8f9fa; 
            padding: 10px; border-radius: 4px; border: 1px solid #dee2e6; 
        }
        @keyframes fadeIn { from { opacity: 0; transform: translateY(-10px); } to { opacity: 1; transform: translateY(0); } }
        .btn:disabled { opacity: 0.6; cursor: not-allowed; }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h1 class="text-center mb-4">Demo de Búsqueda Semántica POC</h1>
        <p class="text-center text-muted mb-5">Generación automática de descripciones con Amazon Nova Micro</p>
        
        <!-- Sección para subir productos -->
        <div class="card mb-4">
            <div class="card-header">
                <h2 class="h5 mb-0">1. Subir Producto con Descripción Automática</h2>
            </div>
            <div class="card-body">
                <form id="uploadForm" class="row g-3">
                    <div class="col-md-6">
                        <label for="imageFile" class="form-label">Selecciona una imagen del producto</label>
                        <input type="file" class="form-control" id="imageFile" accept="image/*" required>
                    </div>
                    <div class="col-md-4">
                        <label for="company" class="form-label">Empresa</label>
                        <select class="form-control" id="company" required>
                            <option value="">Selecciona una empresa</option>
                            <option value="COLOMBIA">COLOMBIA</option>
                            <option value="MEXICO">MEXICO</option>
                            <option value="BRASIL">BRASIL</option>
                            <option value="Otra">Otra</option>
                        </select>
                    </div>
                    <div class="col-md-2 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary w-100">Analizar</button>
                    </div>
                </form>
                
                <!-- Vista previa de descripción generada -->
                <div id="descriptionPreview" class="mt-4" style="display: none;">
                    <div class="alert alert-info">
                        <h6 class="alert-heading">Descripción generada automáticamente con Nova Micro:</h6>
                        <p id="generatedDescription" class="mb-2"></p>
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-success btn-sm" id="confirmDescription">Confirmar y Guardar</button>
                            <button type="button" class="btn btn-secondary btn-sm" id="cancelUpload">Cancelar</button>
                        </div>
                    </div>
                </div>

                <!-- Mensajes de error -->
                <div id="errorMessage" class="mt-3" style="display: none;">
                    <div class="alert alert-danger">
                        <h6 class="alert-heading">Error:</h6>
                        <p id="errorText" class="mb-0"></p>
                    </div>
                </div>

                <!-- Información sobre formatos -->
                <div class="mt-3">
                    <small class="text-muted">
                        <strong>Formatos soportados:</strong> JPG, PNG, GIF, WebP (máximo 10MB)<br>
                        <strong>Modelo:</strong> Amazon Nova Micro (el más económico)
                    </small>
                </div>
            </div>
        </div>
        
        <!-- Sección de productos guardados -->
        <div class="card mb-4">
            <div class="card-header">
                <h2 class="h5 mb-0">2. Productos con Descripciones Automáticas</h2>
            </div>
            <div class="card-body">
                <div id="productGallery" class="row">
                    <div class="col-12 text-center" id="noProductsMessage">
                        <p class="text-muted">No hay productos cargados aún.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Variables globales
        let tempProductData = null;
        const API_BASE = '/api/v1/files';

        // Configurar eventos al cargar la página
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('uploadForm').addEventListener('submit', handleImageUpload);
            document.getElementById('confirmDescription').addEventListener('click', confirmAndSaveProduct);
            document.getElementById('cancelUpload').addEventListener('click', cancelUpload);
        });

        // Función para manejar la subida y análisis de imagen
        async function handleImageUpload(e) {
            e.preventDefault();
            
            const fileInput = document.getElementById('imageFile');
            const companyInput = document.getElementById('company');

            if (!fileInput.files[0] || !companyInput.value) {
                showError('Por favor selecciona una imagen y especifica la empresa');
                return;
            }

            // Validar formato y tamaño
            const file = fileInput.files[0];
            const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
            if (!allowedTypes.includes(file.type)) {
                showError('Formato de imagen no válido. Por favor usa JPG, PNG, GIF o WebP');
                return;
            }

            if (file.size > 10 * 1024 * 1024) {
                showError('La imagen es demasiado grande. El tamaño máximo es 10MB');
                return;
            }

            const formData = new FormData();
            formData.append('file', file);
            formData.append('company', companyInput.value);

            try {
                const submitBtn = document.querySelector('#uploadForm button[type="submit"]');
                const originalText = submitBtn.textContent;
                submitBtn.textContent = 'Analizando con Nova Micro...';
                submitBtn.disabled = true;

                const response = await fetch(`${API_BASE}/generate-description`, {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.detail || `Error del servidor: ${response.status}`);
                }

                const result = await response.json();

                // Almacenar datos temporales
                tempProductData = {
                    file: file,
                    company: companyInput.value,
                    description: result.descripcion,
                    imagen_base64: result.imagen_base64
                };

                // Mostrar descripción generada
                document.getElementById('generatedDescription').textContent = result.descripcion;
                document.getElementById('descriptionPreview').style.display = 'block';
                hideError();

                submitBtn.textContent = originalText;
                submitBtn.disabled = false;

            } catch (error) {
                console.error('Error:', error);
                showError('Error al procesar la imagen: ' + error.message);
                
                const submitBtn = document.querySelector('#uploadForm button[type="submit"]');
                submitBtn.textContent = 'Analizar';
                submitBtn.disabled = false;
            }
        }

        // Función para confirmar y guardar el producto
        async function confirmAndSaveProduct() {
            if (!tempProductData) {
                showError('No hay datos de producto para guardar');
                return;
            }

            try {
                const formData = new FormData();
                formData.append('file', tempProductData.file);
                formData.append('company', tempProductData.company);
                formData.append('description', tempProductData.description);

                const response = await fetch(`${API_BASE}/upload`, {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.detail || `Error del servidor: ${response.status}`);
                }

                // Limpiar formulario
                document.getElementById('imageFile').value = '';
                document.getElementById('company').value = '';
                document.getElementById('descriptionPreview').style.display = 'none';
                tempProductData = null;
                hideError();

                showSuccess('Producto guardado exitosamente con descripción automática');

            } catch (error) {
                console.error('Error:', error);
                showError('Error al guardar el producto: ' + error.message);
            }
        }

        // Función para cancelar
        function cancelUpload() {
            tempProductData = null;
            document.getElementById('descriptionPreview').style.display = 'none';
            document.getElementById('imageFile').value = '';
            document.getElementById('company').value = '';
            hideError();
        }

        // Funciones de utilidad para mostrar mensajes
        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            const errorText = document.getElementById('errorText');
            errorText.textContent = message;
            errorDiv.style.display = 'block';
            setTimeout(() => errorDiv.style.display = 'none', 5000);
        }

        function hideError() {
            document.getElementById('errorMessage').style.display = 'none';
        }

        function showSuccess(message) {
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-success alert-dismissible fade show';
            alertDiv.innerHTML = `
                <strong>Éxito:</strong> ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.querySelector('.container').insertBefore(alertDiv, document.querySelector('.card'));
            setTimeout(() => alertDiv.remove(), 5000);
        }
    </script>
</body>
</html>