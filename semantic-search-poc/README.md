# Semantic Search POC

A Proof of Concept implementation for semantic search using PostgreSQL with pgvector extension, following the specifications in `semantic.md`.

## Overview

This POC implements a semantic search system that allows users to search for products using natural language queries. The system:

1. **Stores product images with descriptions** in PostgreSQL tables (one per company/country)
2. **Generates embeddings** using OpenAI's text-embedding-ada-002 model
3. **Uses vector similarity search** with pgvector's HNSW index and cosine distance
4. **Provides REST API endpoints** for managing companies, images, and searching

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   FastAPI App   │    │   PostgreSQL     │    │   OpenAI API    │
│                 │    │   + pgvector     │    │                 │
│ • Company CRUD  │◄──►│ • Dynamic tables │    │ • Embeddings    │
│ • Image Insert  │    │ • Vector indices │    │ • LLM for desc  │
│ • Search        │    │ • Unified views  │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## Database Schema

Each company gets its own table following this structure:

```sql
CREATE TABLE IMAGENES_COLOMBIA (
    empresa VARCHAR(255) NOT NULL,
    idProducto INT NOT NULL,
    idPhoto INT NOT NULL,
    urlPhoto TEXT,
    texto TEXT,
    embedding VECTOR(1536),
    PRIMARY KEY (empresa, idProducto, idPhoto)
);

-- Vector index for fast similarity search
CREATE INDEX idx_imagenes_colombia ON IMAGENES_COLOMBIA 
USING HNSW (embedding vector_cosine_ops);

-- Unified view across all companies
CREATE VIEW IMAGENES AS
SELECT empresa, idProducto, idPhoto, urlPhoto, texto, embedding
FROM IMAGENES_COLOMBIA
UNION ALL
SELECT empresa, idProducto, idPhoto, urlPhoto, texto, embedding
FROM IMAGENES_PERU;
```

## Prerequisites

- Python 3.8+
- Docker and Docker Compose
- OpenAI API key

## Setup Instructions

### 1. Clone and Navigate

```bash
cd semantic-search-poc
```

### 2. Install Dependencies

```bash
pip install -r requirements.txt
```

### 3. Set Up Environment Variables

```bash
cp .env.example .env
```

Edit `.env` and add your OpenAI API key:

```env
OPENAI_API_KEY=your_openai_api_key_here
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/semantic_search
APP_HOST=0.0.0.0
APP_PORT=8000
DEBUG=True
```

### 4. Start PostgreSQL with pgvector

```bash
docker-compose up -d
```

Wait for PostgreSQL to be ready:

```bash
docker-compose logs postgres
```

### 5. Start the API

```bash
python -m app.main
```

The API will be available at:
- **API**: http://localhost:8000
- **Interactive docs**: http://localhost:8000/docs
- **Health check**: http://localhost:8000/health

## API Endpoints

### 1. Create Company/Country

Creates a new table for a company with vector index.

```bash
POST /api/v1/companies/{company_name}/create
```

Example:
```bash
curl -X POST "http://localhost:8000/api/v1/companies/COLOMBIA/create" \
     -H "Content-Type: application/json" \
     -d "{}"
```

### 2. Reindex Company

Recreates the vector index for a company.

```bash
POST /api/v1/companies/{company_name}/reindex
```

### 3. Insert Image

Inserts a product image with automatic description generation and embedding.

```bash
POST /api/v1/companies/{company_name}/images
```

Example:
```bash
curl -X POST "http://localhost:8000/api/v1/companies/COLOMBIA/images" \
     -H "Content-Type: application/json" \
     -d '{
       "idProducto": 1,
       "idPhoto": 1,
       "urlPhoto": "https://example.com/zapatos.jpg",
       "texto": "Zapatos negros de tacón alto para mujer"
     }'
```

### 4. Semantic Search

Searches for similar products using natural language.

```bash
POST /api/v1/search
```

Example:
```bash
curl -X POST "http://localhost:8000/api/v1/search" \
     -H "Content-Type: application/json" \
     -d '{
       "company_name": "COLOMBIA",
       "query_text": "busco zapatos negros con tacón para mujer",
       "limit": 5
     }'
```

## Testing and Validation

### Run Unit Tests

```bash
pytest tests/ -v
```

### Run Integration Tests

```bash
pytest tests/ -v -m integration
```

### Load Sample Data and Test

```bash
python sample_data.py
```

This script will:
1. Check API health
2. Create sample companies (COLOMBIA, PERU)
3. Insert sample products with descriptions
4. Test semantic searches with various queries

## Example Usage Workflow

1. **Start the system**:
   ```bash
   docker-compose up -d
   python -m app.main
   ```

2. **Create a company**:
   ```bash
   curl -X POST "http://localhost:8000/api/v1/companies/COLOMBIA/create" -d "{}"
   ```

3. **Add products**:
   ```bash
   curl -X POST "http://localhost:8000/api/v1/companies/COLOMBIA/images" \
        -H "Content-Type: application/json" \
        -d '{
          "idProducto": 1,
          "idPhoto": 1,
          "texto": "Zapatos negros de tacón alto para mujer, elegantes y cómodos"
        }'
   ```

4. **Search semantically**:
   ```bash
   curl -X POST "http://localhost:8000/api/v1/search" \
        -H "Content-Type: application/json" \
        -d '{
          "company_name": "COLOMBIA",
          "query_text": "quiero zapatos negros con tacón para ocasiones formales",
          "limit": 10
        }'
   ```

## Key Features Implemented

✅ **Dynamic table creation** per company/country  
✅ **Vector embeddings** using OpenAI text-embedding-ada-002 (1536 dimensions)  
✅ **HNSW vector index** with cosine distance for fast similarity search  
✅ **Unified view** across all company tables  
✅ **LLM-powered description generation** for images  
✅ **Query cleaning** to extract price information  
✅ **RESTful API** with comprehensive error handling  
✅ **Health checks** and monitoring  
✅ **Docker setup** for easy local development  

## Limitations (POC Scope)

- Uses OpenAI API instead of AWS Titan (easier setup for POC)
- No authentication/authorization
- No rate limiting
- Basic error handling
- No image processing (descriptions only)
- No caching layer

## Production Considerations

For production deployment, consider:

1. **Security**: Add authentication, input validation, rate limiting
2. **Scalability**: Connection pooling, caching, load balancing
3. **Monitoring**: Logging, metrics, alerting
4. **AWS Integration**: Switch to AWS Bedrock/Titan as specified
5. **Image Processing**: Add actual image analysis capabilities
6. **Data Management**: Backup strategies, data retention policies

## Troubleshooting

### Database Connection Issues
```bash
# Check if PostgreSQL is running
docker-compose ps

# View PostgreSQL logs
docker-compose logs postgres

# Restart PostgreSQL
docker-compose restart postgres
```

### OpenAI API Issues
- Verify your API key in `.env`
- Check your OpenAI account has sufficient credits
- Monitor rate limits

### API Issues
```bash
# Check API health
curl http://localhost:8000/health

# View API logs
python -m app.main  # Check console output
```

## License

This is a Proof of Concept implementation for demonstration purposes.