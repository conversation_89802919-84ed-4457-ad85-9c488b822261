# AWS Configuration for Lambda deployment
AWS_REGION=us-east-2
AWS_PROFILE=IA

# Database Configuration - AWS RDS Aurora PostgreSQL (existing)
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres

# Storage Configuration - Use S3 for Lambda deployment
STORAGE_TYPE=s3
# S3_BUCKET_NAME will be set by Terraform

# Application Configuration
APP_HOST=0.0.0.0
APP_PORT=8000
DEBUG=False

# Lambda-specific settings
AWS_LAMBDA_FUNCTION_NAME=semantic-search-poc-api
