#!/bin/bash

# Script de despliegue completo para Fargate mínimo
set -e

echo "🚀 Iniciando despliegue de Semantic Search POC en Fargate..."

# Variables
AWS_REGION="us-east-2"
AWS_PROFILE="IA"
PROJECT_NAME="semantic-search-poc"

# Paso 1: Inicializar y aplicar Terraform
echo "📋 Paso 1: Desplegando infraestructura con Terraform..."
cd terraform

# Limpiar archivos anteriores si existen
rm -f terraform.tfstate terraform.tfstate.backup .terraform.lock.hcl
rm -rf .terraform

# Usar configuración mínima
cp minimal-fargate.tf main.tf
cp minimal-variables.tf variables.tf
cp minimal-outputs.tf outputs.tf

# Inicializar Terraform
terraform init

# Aplicar configuración
terraform apply -auto-approve

echo "✅ Infraestructura desplegada exitosamente"

# Volver al directorio principal
cd ..

# Paso 2: Construir y subir imagen Docker
echo "📋 Paso 2: Construyendo y subiendo imagen Docker..."
./build-and-push.sh

# Paso 3: Actualizar servicio ECS
echo "📋 Paso 3: Actualizando servicio ECS..."
aws ecs update-service \
    --cluster ${PROJECT_NAME}-cluster \
    --service ${PROJECT_NAME}-service \
    --force-new-deployment \
    --region ${AWS_REGION} \
    --profile ${AWS_PROFILE}

echo "⏳ Esperando que el servicio se estabilice..."
aws ecs wait services-stable \
    --cluster ${PROJECT_NAME}-cluster \
    --services ${PROJECT_NAME}-service \
    --region ${AWS_REGION} \
    --profile ${AWS_PROFILE}

echo "✅ Despliegue completado exitosamente!"

# Mostrar información de acceso
echo ""
echo "📊 Información del despliegue:"
echo "================================"
terraform -chdir=terraform output deployment_info

echo ""
echo "🔍 Para obtener la IP pública de acceso, ejecuta:"
echo "aws ecs describe-tasks --cluster ${PROJECT_NAME}-cluster --tasks \$(aws ecs list-tasks --cluster ${PROJECT_NAME}-cluster --service-name ${PROJECT_NAME}-service --query 'taskArns[0]' --output text --region ${AWS_REGION} --profile ${AWS_PROFILE}) --query 'tasks[0].attachments[0].details[?name==\`networkInterfaceId\`].value' --output text --region ${AWS_REGION} --profile ${AWS_PROFILE} | xargs -I {} aws ec2 describe-network-interfaces --network-interface-ids {} --query 'NetworkInterfaces[0].Association.PublicIp' --output text --region ${AWS_REGION} --profile ${AWS_PROFILE}"