#!/bin/bash

# Build and push Docker image to ECR with ECS service update
set -e

# Configuration
AWS_REGION="us-east-2"
AWS_PROFILE="IA"
PROJECT_NAME="semantic-search-poc"

echo "🔨 Building and pushing Docker image to ECR"
echo "📍 Region: $AWS_REGION"
echo "👤 Profile: $AWS_PROFILE"
echo ""

# Check if terraform outputs exist
if [ ! -f "terraform/terraform.tfstate" ]; then
    echo "❌ Error: Terraform state not found. Please run deploy-fargate.sh first."
    exit 1
fi

# Get ECR repository URL from Terraform
cd terraform
ECR_REPO=$(terraform output -raw ecr_repository_url)
cd ..

echo "📦 ECR Repository: $ECR_REPO"

# Login to ECR
echo "🔐 Logging into ECR..."
aws ecr get-login-password --region $AWS_REGION --profile $AWS_PROFILE | docker login --username AWS --password-stdin $ECR_REPO

# Build Docker image
echo "🔨 Building Docker image..."
docker build -t $PROJECT_NAME .

# Tag image for ECR
echo "🏷️  Tagging image for ECR..."
docker tag $PROJECT_NAME:latest $ECR_REPO:latest

# Push image to ECR
echo "📤 Pushing image to ECR..."
docker push $ECR_REPO:latest

# Update ECS service
echo "🔄 Updating ECS service..."
cd terraform
CLUSTER_NAME=$(terraform output -raw ecs_cluster_name)
SERVICE_NAME=$(terraform output -raw ecs_service_name)

aws ecs update-service \
    --cluster $CLUSTER_NAME \
    --service $SERVICE_NAME \
    --force-new-deployment \
    --region $AWS_REGION \
    --profile $AWS_PROFILE

echo ""
echo "✅ Image updated successfully!"
echo "⏳ ECS service is updating... This may take a few minutes."
echo ""
echo "🔍 To check deployment status:"
echo "   aws ecs describe-services --cluster $CLUSTER_NAME --services $SERVICE_NAME --region $AWS_REGION --profile $AWS_PROFILE"