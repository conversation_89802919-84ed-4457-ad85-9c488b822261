#!/bin/bash
set -e

# Print environment information
echo "Starting application..."
echo "Python version: $(python --version)"
echo "Working directory: $(pwd)"
echo "Files in directory:"
ls -la

# Check if PORT environment variable is set and is a valid integer
if [[ -z "${PORT}" ]]; then
    echo "PORT environment variable is not set, using default port 8000"
    PORT=8000
else
    # Check if PORT is a valid integer
    if ! [[ "${PORT}" =~ ^[0-9]+$ ]]; then
        echo "PORT environment variable is not a valid integer: '${PORT}', using default port 8000"
        PORT=8000
    else
        echo "Using PORT: ${PORT}"
    fi
fi

# Wait for a moment to ensure all services are ready
sleep 5

# Start the application
echo "Launching application with port ${PORT}..."
exec uvicorn app:app --host 0.0.0.0 --port "${PORT}"
