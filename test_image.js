// Create a simple test image and inject it into the dashboard
const testImageBase64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==";

// Inject the image into the page
selectedImage = testImageBase64;
const imagePreview = document.getElementById('imagePreview');
imagePreview.src = 'data:image/png;base64,' + testImageBase64;
imagePreview.style.display = 'block';
document.getElementById('testSelectedButton').disabled = false;

console.log('Test image injected successfully');
