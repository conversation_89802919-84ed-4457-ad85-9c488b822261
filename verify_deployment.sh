#!/bin/bash
set -e

echo "Verifying deployment configuration..."

# Check if .env file exists
if [ ! -f .env ]; then
    echo "Warning: .env file not found. Make sure to create one based on .env.example"
    echo "Creating a temporary .env file for testing..."
    cp .env.example .env
fi

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "Error: Docker is not installed. Please install Docker first."
    exit 1
fi

# Build the Docker image
echo "Building Docker image..."
docker build -t semantic-search-test .

# Run the Docker container
echo "Running Docker container..."
docker run -d --name semantic-search-test-container -p 8000:8000 --env-file .env semantic-search-test

# Wait for the container to start
echo "Waiting for the container to start..."
sleep 10

# Check if the container is running
if [ "$(docker ps -q -f name=semantic-search-test-container)" ]; then
    echo "Container is running."
else
    echo "Error: Container failed to start."
    docker logs semantic-search-test-container
    docker rm -f semantic-search-test-container 2>/dev/null || true
    exit 1
fi

# Check if the health endpoint is responding
echo "Checking health endpoint..."
if curl -s http://localhost:8000/health | grep -q "ok"; then
    echo "Health check passed!"
else
    echo "Error: Health check failed."
    docker logs semantic-search-test-container
    docker rm -f semantic-search-test-container 2>/dev/null || true
    exit 1
fi

# Clean up
echo "Cleaning up..."
docker rm -f semantic-search-test-container

echo "Verification completed successfully!"
echo "Your application should now be ready for deployment to Railway."
