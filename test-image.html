<!DOCTYPE html>
<html>
<head>
    <title>Test Image Generator</title>
</head>
<body>
    <canvas id="canvas" width="200" height="200"></canvas>
    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        
        // Create a simple test image
        ctx.fillStyle = '#4CAF50';
        ctx.fillRect(0, 0, 200, 200);
        
        ctx.fillStyle = '#ffffff';
        ctx.font = '20px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('TEST', 100, 100);
        ctx.fillText('IMAGE', 100, 130);
        
        // Convert to blob and download
        canvas.toBlob(function(blob) {
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'test-image.png';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        });
    </script>
</body>
</html>
