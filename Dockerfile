FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Set environment variables
ENV PORT=8000
ENV PYTHONUNBUFFERED=1

# Test AWS Bedrock connectivity during build (optional)
# RUN python -c "import boto3; print('AWS SDK installed successfully')"

# Create a healthcheck script that handles PORT variable correctly
RUN echo '#!/bin/bash\nPORT=${PORT:-8000}\ncurl -f http://localhost:$PORT/health || exit 1' > /app/healthcheck.sh && \
    chmod +x /app/healthcheck.sh

# Add healthcheck
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 CMD /app/healthcheck.sh

# Make start script executable
RUN chmod +x /app/start.sh

# Run the application using the start script
CMD ["/app/start.sh"]
